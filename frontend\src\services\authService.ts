import { ApiClient } from './api';
import {
  User,
  UserStats,
  LoginForm,
  RegisterForm,
  UpdateProfileForm,
  PasswordResetForm,
  NewPasswordForm,
  SocialBindingForm,
  Platform,
} from '@/types';

export class AuthService {
  /**
   * 用户注册
   */
  static async register(data: RegisterForm) {
    return ApiClient.post<{
      user: User;
      tokens: { accessToken: string; refreshToken: string };
    }>('/auth/register', data);
  }

  /**
   * 用户登录
   */
  static async login(data: LoginForm) {
    return ApiClient.post<{
      user: User;
      tokens: { accessToken: string; refreshToken: string };
    }>('/auth/login', data);
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser() {
    return ApiClient.get<{
      user: User;
      stats: UserStats;
    }>('/auth/me');
  }

  /**
   * 更新用户资料
   */
  static async updateProfile(data: UpdateProfileForm) {
    return ApiClient.put<{ user: User }>('/auth/profile', data);
  }

  /**
   * 请求密码重置
   */
  static async requestPasswordReset(data: PasswordResetForm) {
    return ApiClient.post('/auth/password/reset-request', data);
  }

  /**
   * 重置密码
   */
  static async resetPassword(data: NewPasswordForm) {
    return ApiClient.post('/auth/password/reset', data);
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string) {
    return ApiClient.get(`/auth/verify-email/${token}`);
  }

  /**
   * 绑定社交账号
   */
  static async bindSocialAccount(data: SocialBindingForm) {
    return ApiClient.post<{
      social_accounts: User['social_accounts'];
    }>('/auth/social/bind', data);
  }

  /**
   * 解绑社交账号
   */
  static async unbindSocialAccount(platform: Platform) {
    return ApiClient.delete<{
      social_accounts: User['social_accounts'];
    }>(`/auth/social/unbind/${platform}`);
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(refreshToken: string) {
    return ApiClient.post<{
      accessToken: string;
      refreshToken: string;
    }>('/auth/refresh', { refreshToken });
  }

  /**
   * 登出
   */
  static async logout() {
    return ApiClient.post('/auth/logout');
  }

  /**
   * 检查用户名是否可用
   */
  static async checkUsernameAvailability(username: string) {
    return ApiClient.get<{ available: boolean }>(`/auth/check-username/${username}`);
  }

  /**
   * 检查邮箱是否可用
   */
  static async checkEmailAvailability(email: string) {
    return ApiClient.get<{ available: boolean }>(`/auth/check-email/${email}`);
  }

  /**
   * 重新发送验证邮件
   */
  static async resendVerificationEmail() {
    return ApiClient.post('/auth/resend-verification');
  }

  /**
   * 更改密码
   */
  static async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }) {
    return ApiClient.post('/auth/change-password', data);
  }

  /**
   * 删除账号
   */
  static async deleteAccount(password: string) {
    return ApiClient.delete('/auth/account', {
      data: { password },
    });
  }

  /**
   * 获取登录历史
   */
  static async getLoginHistory(page = 1, limit = 20) {
    return ApiClient.get<{
      history: Array<{
        id: number;
        ip_address: string;
        user_agent: string;
        login_at: string;
        location?: string;
      }>;
      pagination: any;
    }>(`/auth/login-history?page=${page}&limit=${limit}`);
  }

  /**
   * 启用/禁用两步验证
   */
  static async toggleTwoFactorAuth(enabled: boolean) {
    return ApiClient.post<{
      enabled: boolean;
      qrCode?: string;
      backupCodes?: string[];
    }>('/auth/2fa/toggle', { enabled });
  }

  /**
   * 验证两步验证码
   */
  static async verifyTwoFactorCode(code: string) {
    return ApiClient.post<{ valid: boolean }>('/auth/2fa/verify', { code });
  }
}
