import { Request, Response } from 'express';
import { ApiResponse } from '@/types';
import { asyncHandler } from '@/middleware/errorHandler';

/**
 * 演示模式信息
 */
export const getDemoInfo = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.json({
    success: true,
    message: '当前运行在演示模式',
    data: {
      mode: 'demo',
      features: [
        '✅ 前端界面展示',
        '✅ API接口测试',
        '✅ 基础功能演示',
        '❌ 用户注册/登录（需要数据库）',
        '❌ 任务管理（需要数据库）',
        '❌ 积分系统（需要数据库）',
      ],
      setup: {
        title: '启用完整功能',
        steps: [
          '1. 安装并启动PostgreSQL数据库',
          '2. 配置backend/.env文件中的数据库连接',
          '3. 运行数据库迁移: npm run db:migrate',
          '4. 插入种子数据: npm run db:seed',
          '5. 重启后端服务器',
        ],
        docker: 'docker run --name postgres-mutual-like -e POSTGRES_PASSWORD=postgres123 -e POSTGRES_DB=mutual_like_platform -p 5432:5432 -d postgres:15'
      }
    }
  });
});

/**
 * 演示用户数据
 */
export const getDemoUser = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.json({
    success: true,
    data: {
      user: {
        id: 1,
        username: 'demo_user',
        email: '<EMAIL>',
        points_balance: 150,
        email_verified: true,
        social_accounts: {
          douyin: {
            user_id: 'demo123',
            username: 'demo_douyin',
            verified: true,
          }
        },
        created_at: new Date().toISOString(),
      },
      stats: {
        totalTasksPublished: 5,
        totalTasksCompleted: 12,
        totalPointsEarned: 180,
        totalPointsSpent: 30,
      }
    }
  });
});

/**
 * 演示任务数据
 */
export const getDemoTasks = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const mockTasks = [
    {
      id: 1,
      title: '抖音美食视频点赞',
      description: '为我的美食制作视频点赞，内容健康正面',
      video_url: 'https://douyin.com/video/123',
      platform: 'douyin',
      task_type: 'like',
      reward_points: 5,
      total_quota: 100,
      completed_count: 23,
      status: 'active',
      expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      publisher_username: 'foodlover123',
      created_at: new Date().toISOString(),
    },
    {
      id: 2,
      title: '快手舞蹈视频分享',
      description: '分享我的舞蹈视频到朋友圈或其他平台',
      video_url: 'https://kuaishou.com/video/456',
      platform: 'kuaishou',
      task_type: 'share',
      reward_points: 8,
      total_quota: 50,
      completed_count: 12,
      status: 'active',
      expires_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
      publisher_username: 'dancer_girl',
      created_at: new Date().toISOString(),
    },
    {
      id: 3,
      title: '小红书旅游笔记关注',
      description: '关注我的小红书账号，分享旅游攻略',
      video_url: 'https://xiaohongshu.com/user/789',
      platform: 'xiaohongshu',
      task_type: 'follow',
      reward_points: 10,
      total_quota: 30,
      completed_count: 8,
      status: 'active',
      expires_at: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(),
      publisher_username: 'travel_expert',
      created_at: new Date().toISOString(),
    },
  ];

  res.json({
    success: true,
    data: {
      tasks: mockTasks,
      total: mockTasks.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    }
  });
});

/**
 * 演示积分记录
 */
export const getDemoPoints = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const mockTransactions = [
    {
      id: 1,
      amount: 5,
      transaction_type: 'reward',
      description: '完成点赞任务',
      task_title: '抖音美食视频点赞',
      balance_after: 150,
      created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 2,
      amount: -20,
      transaction_type: 'cost',
      description: '发布新任务',
      task_title: '快手舞蹈视频分享',
      balance_after: 145,
      created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    },
    {
      id: 3,
      amount: 10,
      transaction_type: 'bonus',
      description: '连续签到7天奖励',
      balance_after: 165,
      created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    },
  ];

  res.json({
    success: true,
    data: {
      transactions: mockTransactions,
      total: mockTransactions.length,
      totalPages: 1,
    }
  });
});
