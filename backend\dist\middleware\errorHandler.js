"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.handleJWTError = exports.handleDatabaseError = exports.handleValidationErrors = exports.globalErrorHandler = exports.notFoundHandler = exports.asyncHandler = exports.AppError = void 0;
const logger_1 = __importDefault(require("@/utils/logger"));
class AppError extends Error {
    constructor(message, statusCode = 500) {
        super(message);
        this.statusCode = statusCode;
        this.isOperational = true;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.AppError = AppError;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
const notFoundHandler = (req, res, next) => {
    const error = new AppError(`路径 ${req.originalUrl} 不存在`, 404);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
const globalErrorHandler = (error, req, res, _next) => {
    let statusCode = error.statusCode || 500;
    let message = error.message || '服务器内部错误';
    logger_1.default.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        userId: req.user?.userId,
    });
    if (error.name === 'ValidationError') {
        statusCode = 400;
        message = '数据验证失败';
    }
    else if (error.name === 'CastError') {
        statusCode = 400;
        message = '无效的数据格式';
    }
    else if (error.code === '23505') {
        statusCode = 409;
        message = '数据已存在';
    }
    else if (error.code === '23503') {
        statusCode = 400;
        message = '关联数据不存在';
    }
    else if (error.name === 'JsonWebTokenError') {
        statusCode = 401;
        message = '无效的访问令牌';
    }
    else if (error.name === 'TokenExpiredError') {
        statusCode = 401;
        message = '访问令牌已过期';
    }
    const response = {
        success: false,
        error: message,
    };
    if (process.env['NODE_ENV'] === 'development') {
        response.error = error.message;
        response.stack = error.stack;
    }
    res.status(statusCode).json(response);
};
exports.globalErrorHandler = globalErrorHandler;
const handleValidationErrors = (req, res, next) => {
    const { validationResult } = require('express-validator');
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map((error) => error.msg);
        res.status(400).json({
            success: false,
            error: '数据验证失败',
            message: errorMessages.join(', '),
        });
        return;
    }
    next();
};
exports.handleValidationErrors = handleValidationErrors;
const handleDatabaseError = (error) => {
    if (error.code === '23505') {
        return new AppError('数据已存在', 409);
    }
    if (error.code === '23503') {
        return new AppError('关联数据不存在', 400);
    }
    if (error.code === '23502') {
        return new AppError('必填字段不能为空', 400);
    }
    return new AppError('数据库操作失败', 500);
};
exports.handleDatabaseError = handleDatabaseError;
const handleJWTError = (error) => {
    if (error.name === 'JsonWebTokenError') {
        return new AppError('无效的访问令牌', 401);
    }
    if (error.name === 'TokenExpiredError') {
        return new AppError('访问令牌已过期', 401);
    }
    return new AppError('认证失败', 401);
};
exports.handleJWTError = handleJWTError;
//# sourceMappingURL=errorHandler.js.map