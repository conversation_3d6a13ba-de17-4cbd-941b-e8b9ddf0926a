import { db } from '@/config/database';
import { User, SocialAccounts } from '@/types';
import bcrypt from 'bcryptjs';
import crypto from 'crypto';

export class UserModel {
  private static tableName = 'users';

  /**
   * 根据ID查找用户
   */
  static async findById(id: number): Promise<User | null> {
    const user = await db(this.tableName).where({ id }).first();
    return user || null;
  }

  /**
   * 根据邮箱查找用户
   */
  static async findByEmail(email: string): Promise<User | null> {
    const user = await db(this.tableName).where({ email }).first();
    return user || null;
  }

  /**
   * 根据用户名查找用户
   */
  static async findByUsername(username: string): Promise<User | null> {
    const user = await db(this.tableName).where({ username }).first();
    return user || null;
  }

  /**
   * 创建新用户
   */
  static async create(userData: {
    username: string;
    email: string;
    password: string;
    points_balance?: number;
  }): Promise<User> {
    const hashedPassword = await bcrypt.hash(userData.password, 12);
    const emailVerificationToken = crypto.randomBytes(32).toString('hex');

    const [user] = await db(this.tableName)
      .insert({
        username: userData.username,
        email: userData.email,
        password_hash: hashedPassword,
        points_balance: userData.points_balance || 100,
        email_verification_token: emailVerificationToken,
        social_accounts: JSON.stringify({}),
      })
      .returning('*');

    return user;
  }

  /**
   * 验证密码
   */
  static async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  /**
   * 更新用户信息
   */
  static async update(id: number, updateData: Partial<User>): Promise<User | null> {
    const [user] = await db(this.tableName)
      .where({ id })
      .update({
        ...updateData,
        updated_at: new Date(),
      })
      .returning('*');

    return user || null;
  }

  /**
   * 更新积分余额
   */
  static async updatePointsBalance(id: number, amount: number): Promise<User | null> {
    const [user] = await db(this.tableName)
      .where({ id })
      .increment('points_balance', amount)
      .returning('*');

    return user || null;
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string): Promise<User | null> {
    const [user] = await db(this.tableName)
      .where({ email_verification_token: token })
      .update({
        email_verified: true,
        email_verification_token: null,
        updated_at: new Date(),
      })
      .returning('*');

    return user || null;
  }

  /**
   * 设置密码重置令牌
   */
  static async setPasswordResetToken(email: string): Promise<string | null> {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 3600000); // 1小时后过期

    const [user] = await db(this.tableName)
      .where({ email })
      .update({
        password_reset_token: token,
        password_reset_expires: expires,
        updated_at: new Date(),
      })
      .returning('*');

    return user ? token : null;
  }

  /**
   * 重置密码
   */
  static async resetPassword(token: string, newPassword: string): Promise<User | null> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    const [user] = await db(this.tableName)
      .where({ password_reset_token: token })
      .where('password_reset_expires', '>', new Date())
      .update({
        password_hash: hashedPassword,
        password_reset_token: null,
        password_reset_expires: null,
        updated_at: new Date(),
      })
      .returning('*');

    return user || null;
  }

  /**
   * 更新社交账号绑定
   */
  static async updateSocialAccounts(id: number, socialAccounts: SocialAccounts): Promise<User | null> {
    const [user] = await db(this.tableName)
      .where({ id })
      .update({
        social_accounts: JSON.stringify(socialAccounts),
        updated_at: new Date(),
      })
      .returning('*');

    return user || null;
  }

  /**
   * 更新最后登录信息
   */
  static async updateLastLogin(id: number, ip: string): Promise<void> {
    await db(this.tableName)
      .where({ id })
      .update({
        last_login_ip: ip,
        last_login_at: new Date(),
        updated_at: new Date(),
      });
  }

  /**
   * 检查用户名是否存在
   */
  static async isUsernameExists(username: string): Promise<boolean> {
    const user = await db(this.tableName).where({ username }).first();
    return !!user;
  }

  /**
   * 检查邮箱是否存在
   */
  static async isEmailExists(email: string): Promise<boolean> {
    const user = await db(this.tableName).where({ email }).first();
    return !!user;
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(id: number): Promise<{
    totalTasksPublished: number;
    totalTasksCompleted: number;
    totalPointsEarned: number;
    totalPointsSpent: number;
  }> {
    const publishedTasks = await db('tasks')
      .where({ publisher_id: id })
      .count('* as count');

    const completedTasks = await db('task_executions')
      .where({ executor_id: id, status: 'verified' })
      .count('* as count');

    const pointsEarned = await db('point_transactions')
      .where({ user_id: id })
      .where('amount', '>', 0)
      .sum('amount as total');

    const pointsSpent = await db('point_transactions')
      .where({ user_id: id })
      .where('amount', '<', 0)
      .sum('amount as total');

    return {
      totalTasksPublished: Number(publishedTasks[0]?.count) || 0,
      totalTasksCompleted: Number(completedTasks[0]?.count) || 0,
      totalPointsEarned: Number(pointsEarned[0]?.total) || 0,
      totalPointsSpent: Math.abs(Number(pointsSpent[0]?.total)) || 0,
    };
  }
}
