import { Router } from 'express';
import {
  register,
  login,
  getCurrentUser,
  updateProfile,
  requestPasswordReset,
  resetPassword,
  verifyEmail,
  bindSocialAccount,
  unbindSocialAccount,
} from '@/controllers/authController';
import { authenticateToken } from '@/middleware/auth';
import { handleValidationErrors } from '@/middleware/errorHandler';
import {
  loginLimiter,
  registerLimiter,
  passwordResetLimiter,
} from '@/middleware/rateLimiter';
import {
  validateRegister,
  validateLogin,
  validatePasswordReset,
  validateNewPassword,
  validateSocialBinding,
} from '@/utils/validation';

const router = Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         id:
 *           type: integer
 *           description: 用户ID
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           description: 邮箱
 *         points_balance:
 *           type: integer
 *           description: 积分余额
 *         email_verified:
 *           type: boolean
 *           description: 邮箱是否已验证
 *         social_accounts:
 *           type: object
 *           description: 绑定的社交账号
 *         created_at:
 *           type: string
 *           format: date-time
 *           description: 创建时间
 *     
 *     LoginRequest:
 *       type: object
 *       required:
 *         - email
 *         - password
 *       properties:
 *         email:
 *           type: string
 *           format: email
 *           description: 邮箱
 *         password:
 *           type: string
 *           description: 密码
 *     
 *     RegisterRequest:
 *       type: object
 *       required:
 *         - username
 *         - email
 *         - password
 *       properties:
 *         username:
 *           type: string
 *           description: 用户名
 *         email:
 *           type: string
 *           format: email
 *           description: 邮箱
 *         password:
 *           type: string
 *           description: 密码
 *     
 *     ApiResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: 请求是否成功
 *         message:
 *           type: string
 *           description: 响应消息
 *         data:
 *           type: object
 *           description: 响应数据
 *         error:
 *           type: string
 *           description: 错误信息
 */

/**
 * @swagger
 * /auth/register:
 *   post:
 *     summary: 用户注册
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterRequest'
 *     responses:
 *       201:
 *         description: 注册成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       400:
 *         description: 请求参数错误
 *       409:
 *         description: 用户名或邮箱已存在
 */
router.post('/register', registerLimiter, validateRegister, handleValidationErrors, register);

/**
 * @swagger
 * /auth/login:
 *   post:
 *     summary: 用户登录
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: 登录成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       401:
 *         description: 邮箱或密码错误
 *       403:
 *         description: 账号已被禁用
 */
router.post('/login', loginLimiter, validateLogin, handleValidationErrors, login);

/**
 * @swagger
 * /auth/me:
 *   get:
 *     summary: 获取当前用户信息
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *       401:
 *         description: 未登录或令牌无效
 */
router.get('/me', authenticateToken, getCurrentUser);

/**
 * @swagger
 * /auth/profile:
 *   put:
 *     summary: 更新用户信息
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               username:
 *                 type: string
 *                 description: 新用户名
 *     responses:
 *       200:
 *         description: 更新成功
 *       401:
 *         description: 未登录
 *       409:
 *         description: 用户名已被使用
 */
router.put('/profile', authenticateToken, updateProfile);

/**
 * @swagger
 * /auth/password/reset-request:
 *   post:
 *     summary: 请求密码重置
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: 邮箱
 *     responses:
 *       200:
 *         description: 重置链接已发送
 */
router.post('/password/reset-request', passwordResetLimiter, validatePasswordReset, handleValidationErrors, requestPasswordReset);

/**
 * @swagger
 * /auth/password/reset:
 *   post:
 *     summary: 重置密码
 *     tags: [认证]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - token
 *               - password
 *             properties:
 *               token:
 *                 type: string
 *                 description: 重置令牌
 *               password:
 *                 type: string
 *                 description: 新密码
 *     responses:
 *       200:
 *         description: 密码重置成功
 *       400:
 *         description: 令牌无效或已过期
 */
router.post('/password/reset', validateNewPassword, handleValidationErrors, resetPassword);

/**
 * @swagger
 * /auth/verify-email/{token}:
 *   get:
 *     summary: 验证邮箱
 *     tags: [认证]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: 验证令牌
 *     responses:
 *       200:
 *         description: 邮箱验证成功
 *       400:
 *         description: 验证令牌无效
 */
router.get('/verify-email/:token', verifyEmail);

/**
 * @swagger
 * /auth/social/bind:
 *   post:
 *     summary: 绑定社交账号
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - platform
 *               - user_id
 *               - username
 *             properties:
 *               platform:
 *                 type: string
 *                 enum: [douyin, kuaishou, xiaohongshu]
 *                 description: 平台类型
 *               user_id:
 *                 type: string
 *                 description: 平台用户ID
 *               username:
 *                 type: string
 *                 description: 平台用户名
 *     responses:
 *       200:
 *         description: 绑定成功
 *       401:
 *         description: 未登录
 */
router.post('/social/bind', authenticateToken, validateSocialBinding, handleValidationErrors, bindSocialAccount);

/**
 * @swagger
 * /auth/social/unbind/{platform}:
 *   delete:
 *     summary: 解绑社交账号
 *     tags: [认证]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: platform
 *         required: true
 *         schema:
 *           type: string
 *           enum: [douyin, kuaishou, xiaohongshu]
 *         description: 平台类型
 *     responses:
 *       200:
 *         description: 解绑成功
 *       401:
 *         description: 未登录
 */
router.delete('/social/unbind/:platform', authenticateToken, unbindSocialAccount);

export default router;
