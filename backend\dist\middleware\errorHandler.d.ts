import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@/types';
export declare class AppError extends Error {
    statusCode: number;
    isOperational: boolean;
    constructor(message: string, statusCode?: number);
}
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, res: Response<ApiResponse>, next: NextFunction) => void;
export declare const globalErrorHandler: (error: any, req: Request, res: Response<ApiResponse>, _next: NextFunction) => void;
export declare const handleValidationErrors: (req: Request, res: Response<ApiResponse>, next: NextFunction) => void;
export declare const handleDatabaseError: (error: any) => AppError;
export declare const handleJWTError: (error: any) => AppError;
//# sourceMappingURL=errorHandler.d.ts.map