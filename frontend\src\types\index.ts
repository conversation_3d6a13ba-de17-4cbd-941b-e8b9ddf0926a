// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  points_balance: number;
  social_accounts: SocialAccounts;
  email_verified: boolean;
  created_at: string;
  last_login_at?: string;
}

export interface SocialAccounts {
  douyin?: {
    user_id: string;
    username: string;
    verified: boolean;
  };
  kuaishou?: {
    user_id: string;
    username: string;
    verified: boolean;
  };
  xiaohongshu?: {
    user_id: string;
    username: string;
    verified: boolean;
  };
}

export interface UserStats {
  totalTasksPublished: number;
  totalTasksCompleted: number;
  totalPointsEarned: number;
  totalPointsSpent: number;
}

// 任务相关类型
export interface Task {
  id: number;
  publisher_id: number;
  title: string;
  description?: string;
  video_url: string;
  platform: Platform;
  task_type: TaskType;
  reward_points: number;
  total_quota: number;
  completed_count: number;
  status: TaskStatus;
  verification_rules: VerificationRules;
  expires_at: string;
  created_at: string;
  updated_at: string;
  publisher_username?: string;
}

export type Platform = 'douyin' | 'kuaishou' | 'xiaohongshu';
export type TaskType = 'like' | 'share' | 'follow' | 'comment';
export type TaskStatus = 'active' | 'paused' | 'completed' | 'expired';

export interface VerificationRules {
  auto_verify?: boolean;
  verification_delay?: number;
  required_proof?: string[];
}

export interface TaskFilters {
  platform?: Platform;
  task_type?: TaskType;
  min_points?: number;
  max_points?: number;
  status?: TaskStatus;
  search?: string;
}

// 积分交易相关类型
export interface PointTransaction {
  id: number;
  user_id: number;
  task_id?: number;
  amount: number;
  transaction_type: TransactionType;
  description: string;
  metadata: Record<string, any>;
  balance_after: number;
  created_at: string;
  updated_at: string;
  task_title?: string;
}

export type TransactionType = 'reward' | 'cost' | 'bonus' | 'refund' | 'penalty';

export interface PointStats {
  earned: number;
  spent: number;
  net: number;
}

// 任务执行相关类型
export interface TaskExecution {
  id: number;
  task_id: number;
  executor_id: number;
  status: ExecutionStatus;
  submitted_at: string;
  verified_at?: string;
  verification_result: VerificationResult;
  ip_address: string;
  user_agent?: string;
  execution_proof: ExecutionProof;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
  task_title?: string;
  reward_points?: number;
  executor_username?: string;
}

export type ExecutionStatus = 'pending' | 'completed' | 'failed' | 'verified' | 'rejected';

export interface VerificationResult {
  success: boolean;
  verified_at?: string;
  verification_method?: string;
  api_response?: any;
  error_message?: string;
}

export interface ExecutionProof {
  screenshot?: string;
  timestamp?: string;
  platform_response?: any;
  additional_data?: Record<string, any>;
}

export interface ExecutionStats {
  total: number;
  pending: number;
  completed: number;
  verified: number;
  failed: number;
  rejected: number;
  successRate: number;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationInfo;
}

// 表单类型
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface CreateTaskForm {
  title: string;
  description?: string;
  video_url: string;
  platform: Platform;
  task_type: TaskType;
  reward_points: number;
  total_quota: number;
  expires_in_days: number;
  verification_rules?: VerificationRules;
}

export interface UpdateProfileForm {
  username: string;
}

export interface PasswordResetForm {
  email: string;
}

export interface NewPasswordForm {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface SocialBindingForm {
  platform: Platform;
  user_id: string;
  username: string;
}

// 认证状态类型
export interface AuthState {
  user: User | null;
  tokens: {
    accessToken: string;
    refreshToken: string;
  } | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

// 应用状态类型
export interface AppState {
  theme: 'light' | 'dark';
  sidebarOpen: boolean;
  notifications: Notification[];
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
}

// 平台配置类型
export interface PlatformConfig {
  name: string;
  displayName: string;
  icon: string;
  color: string;
  urlPattern: RegExp;
  supportedTaskTypes: TaskType[];
}

// 错误类型
export interface AppError {
  message: string;
  code?: string;
  status?: number;
  details?: any;
}

// 路由类型
export interface RouteConfig {
  path: string;
  component: React.ComponentType;
  exact?: boolean;
  protected?: boolean;
  title?: string;
  description?: string;
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps extends BaseComponentProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface InputProps extends BaseComponentProps {
  type?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  error?: string;
  disabled?: boolean;
  required?: boolean;
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface TabsProps extends BaseComponentProps {
  tabs: TabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
  disabled?: boolean;
}
