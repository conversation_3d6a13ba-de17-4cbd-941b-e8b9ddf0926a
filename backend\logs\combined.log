{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m数据库连接失败，服务器启动中止\u001b[39m","timestamp":"2025-06-12 12:20:23:2023"}
{"level":"\u001b[31merror\u001b[39m","message":"\u001b[31m数据库连接失败，服务器启动中止\u001b[39m","timestamp":"2025-06-12 12:21:09:219"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 服务器启动成功\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📍 地址: http://localhost:3000\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API文档: http://localhost:3000/api-docs\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  数据库未连接，某些功能可能无法正常工作\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💡 请配置并启动PostgreSQL数据库以获得完整功能\u001b[39m","timestamp":"2025-06-12 12:21:29:2129"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: 路径 / 不存在\u001b[39m","method":"GET","stack":"Error: 路径 / 不存在\n    at notFoundHandler (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\src\\middleware\\errorHandler.ts:39:17)\n    at Layer.handle [as handle_request] (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 12:21:41:2141","url":"/","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred: 路径 /favicon.ico 不存在\u001b[39m","method":"GET","stack":"Error: 路径 /favicon.ico 不存在\n    at notFoundHandler (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\src\\middleware\\errorHandler.ts:39:17)\n    at Layer.handle [as handle_request] (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:822:7\n    at processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async D:\\AIGC-dm\\Cross-border E-commerce Website Project\\逆向\\backend\\node_modules\\express-rate-limit\\dist\\index.cjs:704:5","timestamp":"2025-06-12 12:21:42:2142","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred:\u001b[39m","method":"POST","stack":"AggregateError\n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1714:7)","timestamp":"2025-06-12 12:22:41:2241","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred:\u001b[39m","method":"POST","stack":"AggregateError\n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1714:7)","timestamp":"2025-06-12 12:22:42:2242","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mError occurred:\u001b[39m","method":"POST","stack":"AggregateError\n    at internalConnectMultiple (node:net:1139:18)\n    at afterConnectMultiple (node:net:1714:7)","timestamp":"2025-06-12 12:22:43:2243","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 服务器启动成功\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📍 地址: http://localhost:3000\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API文档: http://localhost:3000/api-docs\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  数据库未连接，某些功能可能无法正常工作\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💡 请配置并启动PostgreSQL数据库以获得完整功能\u001b[39m","timestamp":"2025-06-12 12:23:58:2358"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 服务器启动成功\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📍 地址: http://localhost:3000\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API文档: http://localhost:3000/api-docs\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  数据库未连接，某些功能可能无法正常工作\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💡 请配置并启动PostgreSQL数据库以获得完整功能\u001b[39m","timestamp":"2025-06-12 12:31:58:3158"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🚀 服务器启动成功\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📍 地址: http://localhost:3000\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m📚 API文档: http://localhost:3000/api-docs\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🏥 健康检查: http://localhost:3000/health\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m🌍 环境: development\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[33mwarn\u001b[39m","message":"\u001b[33m⚠️  数据库未连接，某些功能可能无法正常工作\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m💡 请配置并启动PostgreSQL数据库以获得完整功能\u001b[39m","timestamp":"2025-06-12 12:43:40:4340"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32m收到SIGINT信号，正在关闭服务器...\u001b[39m","timestamp":"2025-06-12 12:46:38:4638"}
