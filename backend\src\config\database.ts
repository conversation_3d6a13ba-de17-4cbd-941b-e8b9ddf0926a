import knex from 'knex';
import { config } from 'dotenv';

config();

const environment = process.env.NODE_ENV || 'development';
const knexConfig = require('../../knexfile.js')[environment];

export const db = knex(knexConfig);

// 数据库连接测试
export const testConnection = async (): Promise<boolean> => {
  try {
    await db.raw('SELECT 1');
    console.log('✅ 数据库连接成功');
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
};

// 优雅关闭数据库连接
export const closeConnection = async (): Promise<void> => {
  try {
    await db.destroy();
    console.log('✅ 数据库连接已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接失败:', error);
  }
};
