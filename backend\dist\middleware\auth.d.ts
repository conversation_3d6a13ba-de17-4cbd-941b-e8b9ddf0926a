import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@/types';
export declare const authenticateToken: (req: Request, res: Response<ApiResponse>, next: NextFunction) => Promise<void>;
export declare const optionalAuth: (req: Request, _res: Response, next: NextFunction) => Promise<void>;
export declare const requireEmailVerification: (req: Request, res: Response<ApiResponse>, next: NextFunction) => Promise<void>;
export declare const requireAdmin: (req: Request, res: Response<ApiResponse>, next: NextFunction) => Promise<void>;
//# sourceMappingURL=auth.d.ts.map