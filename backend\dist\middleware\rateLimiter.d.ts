import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@/types';
export declare const generalLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const loginLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const registerLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const passwordResetLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const taskCreationLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const taskExecutionLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const apiLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const strictLimiter: import("express-rate-limit").RateLimitRequestHandler;
export declare const createUserBasedLimiter: (windowMs: number, max: number, message: string) => (req: Request, res: Response<ApiResponse>, next: NextFunction) => void;
export declare const userTaskExecutionLimiter: (req: Request, res: Response<ApiResponse>, next: NextFunction) => void;
export declare const userTaskCreationLimiter: (req: Request, res: Response<ApiResponse>, next: NextFunction) => void;
//# sourceMappingURL=rateLimiter.d.ts.map