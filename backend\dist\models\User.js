"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserModel = void 0;
const database_1 = require("@/config/database");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const crypto_1 = __importDefault(require("crypto"));
class UserModel {
    static async findById(id) {
        const user = await (0, database_1.db)(this.tableName).where({ id }).first();
        return user || null;
    }
    static async findByEmail(email) {
        const user = await (0, database_1.db)(this.tableName).where({ email }).first();
        return user || null;
    }
    static async findByUsername(username) {
        const user = await (0, database_1.db)(this.tableName).where({ username }).first();
        return user || null;
    }
    static async create(userData) {
        const hashedPassword = await bcryptjs_1.default.hash(userData.password, 12);
        const emailVerificationToken = crypto_1.default.randomBytes(32).toString('hex');
        const [user] = await (0, database_1.db)(this.tableName)
            .insert({
            username: userData.username,
            email: userData.email,
            password_hash: hashedPassword,
            points_balance: userData.points_balance || 100,
            email_verification_token: emailVerificationToken,
            social_accounts: JSON.stringify({}),
        })
            .returning('*');
        return user;
    }
    static async verifyPassword(plainPassword, hashedPassword) {
        return bcryptjs_1.default.compare(plainPassword, hashedPassword);
    }
    static async update(id, updateData) {
        const [user] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .update({
            ...updateData,
            updated_at: new Date(),
        })
            .returning('*');
        return user || null;
    }
    static async updatePointsBalance(id, amount) {
        const [user] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .increment('points_balance', amount)
            .returning('*');
        return user || null;
    }
    static async verifyEmail(token) {
        const [user] = await (0, database_1.db)(this.tableName)
            .where({ email_verification_token: token })
            .update({
            email_verified: true,
            email_verification_token: null,
            updated_at: new Date(),
        })
            .returning('*');
        return user || null;
    }
    static async setPasswordResetToken(email) {
        const token = crypto_1.default.randomBytes(32).toString('hex');
        const expires = new Date(Date.now() + 3600000);
        const [user] = await (0, database_1.db)(this.tableName)
            .where({ email })
            .update({
            password_reset_token: token,
            password_reset_expires: expires,
            updated_at: new Date(),
        })
            .returning('*');
        return user ? token : null;
    }
    static async resetPassword(token, newPassword) {
        const hashedPassword = await bcryptjs_1.default.hash(newPassword, 12);
        const [user] = await (0, database_1.db)(this.tableName)
            .where({ password_reset_token: token })
            .where('password_reset_expires', '>', new Date())
            .update({
            password_hash: hashedPassword,
            password_reset_token: null,
            password_reset_expires: null,
            updated_at: new Date(),
        })
            .returning('*');
        return user || null;
    }
    static async updateSocialAccounts(id, socialAccounts) {
        const [user] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .update({
            social_accounts: JSON.stringify(socialAccounts),
            updated_at: new Date(),
        })
            .returning('*');
        return user || null;
    }
    static async updateLastLogin(id, ip) {
        await (0, database_1.db)(this.tableName)
            .where({ id })
            .update({
            last_login_ip: ip,
            last_login_at: new Date(),
            updated_at: new Date(),
        });
    }
    static async isUsernameExists(username) {
        const user = await (0, database_1.db)(this.tableName).where({ username }).first();
        return !!user;
    }
    static async isEmailExists(email) {
        const user = await (0, database_1.db)(this.tableName).where({ email }).first();
        return !!user;
    }
    static async getUserStats(id) {
        const publishedTasks = await (0, database_1.db)('tasks')
            .where({ publisher_id: id })
            .count('* as count');
        const completedTasks = await (0, database_1.db)('task_executions')
            .where({ executor_id: id, status: 'verified' })
            .count('* as count');
        const pointsEarned = await (0, database_1.db)('point_transactions')
            .where({ user_id: id })
            .where('amount', '>', 0)
            .sum('amount as total');
        const pointsSpent = await (0, database_1.db)('point_transactions')
            .where({ user_id: id })
            .where('amount', '<', 0)
            .sum('amount as total');
        return {
            totalTasksPublished: Number(publishedTasks[0]?.count) || 0,
            totalTasksCompleted: Number(completedTasks[0]?.count) || 0,
            totalPointsEarned: Number(pointsEarned[0]?.total) || 0,
            totalPointsSpent: Math.abs(Number(pointsSpent[0]?.total)) || 0,
        };
    }
}
exports.UserModel = UserModel;
UserModel.tableName = 'users';
//# sourceMappingURL=User.js.map