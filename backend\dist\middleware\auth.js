"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireAdmin = exports.requireEmailVerification = exports.optionalAuth = exports.authenticateToken = void 0;
const jwt_1 = require("@/utils/jwt");
const User_1 = require("@/models/User");
const authenticateToken = async (req, res, next) => {
    try {
        const token = (0, jwt_1.extractTokenFromHeader)(req.headers.authorization);
        if (!token) {
            res.status(401).json({
                success: false,
                error: '未提供访问令牌',
            });
            return;
        }
        const decoded = (0, jwt_1.verifyToken)(token);
        if (!decoded) {
            res.status(401).json({
                success: false,
                error: '无效的访问令牌',
            });
            return;
        }
        const user = await User_1.UserModel.findById(decoded.userId);
        if (!user || !user.is_active) {
            res.status(401).json({
                success: false,
                error: '用户不存在或已被禁用',
            });
            return;
        }
        req.user = decoded;
        next();
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: '认证过程中发生错误',
        });
    }
};
exports.authenticateToken = authenticateToken;
const optionalAuth = async (req, _res, next) => {
    try {
        const token = (0, jwt_1.extractTokenFromHeader)(req.headers.authorization);
        if (token) {
            const decoded = (0, jwt_1.verifyToken)(token);
            if (decoded) {
                const user = await User_1.UserModel.findById(decoded.userId);
                if (user && user.is_active) {
                    req.user = decoded;
                }
            }
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
const requireEmailVerification = async (req, res, next) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: '需要登录',
            });
            return;
        }
        const user = await User_1.UserModel.findById(req.user.userId);
        if (!user || !user.email_verified) {
            res.status(403).json({
                success: false,
                error: '请先验证邮箱',
            });
            return;
        }
        next();
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: '验证邮箱状态时发生错误',
        });
    }
};
exports.requireEmailVerification = requireEmailVerification;
const requireAdmin = async (req, res, next) => {
    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: '需要登录',
            });
            return;
        }
        const user = await User_1.UserModel.findById(req.user.userId);
        if (!user) {
            res.status(401).json({
                success: false,
                error: '用户不存在',
            });
            return;
        }
        next();
    }
    catch (error) {
        res.status(500).json({
            success: false,
            error: '权限验证时发生错误',
        });
    }
};
exports.requireAdmin = requireAdmin;
//# sourceMappingURL=auth.js.map