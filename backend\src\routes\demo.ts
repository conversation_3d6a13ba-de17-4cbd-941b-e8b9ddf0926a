import { Router } from 'express';
import {
  getDemoInfo,
  getDemoUser,
  getDemoTasks,
  getDemoPoints,
} from '@/controllers/demoController';

const router = Router();

/**
 * @swagger
 * /demo/info:
 *   get:
 *     summary: 获取演示模式信息
 *     tags: [演示]
 *     responses:
 *       200:
 *         description: 演示模式信息
 */
router.get('/info', getDemoInfo);

/**
 * @swagger
 * /demo/user:
 *   get:
 *     summary: 获取演示用户数据
 *     tags: [演示]
 *     responses:
 *       200:
 *         description: 演示用户数据
 */
router.get('/user', getDemoUser);

/**
 * @swagger
 * /demo/tasks:
 *   get:
 *     summary: 获取演示任务数据
 *     tags: [演示]
 *     responses:
 *       200:
 *         description: 演示任务数据
 */
router.get('/tasks', getDemoTasks);

/**
 * @swagger
 * /demo/points:
 *   get:
 *     summary: 获取演示积分数据
 *     tags: [演示]
 *     responses:
 *       200:
 *         description: 演示积分数据
 */
router.get('/points', getDemoPoints);

export default router;
