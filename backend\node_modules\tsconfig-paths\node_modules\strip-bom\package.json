{"name": "strip-bom", "version": "3.0.0", "description": "Strip UTF-8 byte order mark (BOM) from a string", "license": "MIT", "repository": "sindresorhus/strip-bom", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=4"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["strip", "bom", "byte", "order", "mark", "unicode", "utf8", "utf-8", "remove", "delete", "trim", "text", "string"], "devDependencies": {"ava": "*", "xo": "*"}}