# 互助点赞平台

基于积分的社交媒体互助系统，支持抖音、快手、小红书等平台的点赞、关注、分享任务。

## 项目特性

- 🎯 **多平台支持**: 支持抖音、快手、小红书等主流社交媒体平台
- 💰 **积分系统**: 完成任务获得积分，发布任务消耗积分
- 🔐 **安全可靠**: JWT认证、防作弊机制、IP限制
- 📱 **响应式设计**: 移动端优先，支持桌面端
- 🚀 **高性能**: Docker容器化部署，支持水平扩展

## 技术栈

### 前端
- React 18 + TypeScript
- Tailwind CSS
- Vite
- React Router
- Axios

### 后端
- Node.js + Express + TypeScript
- PostgreSQL
- JWT认证
- bcrypt密码加密
- nodemailer邮件服务

### 部署
- Docker + Docker Compose
- Nginx反向代理

## 快速开始

### 环境要求
- Node.js 18+
- PostgreSQL 14+ (可选，用于完整功能)
- Docker (可选，用于数据库)

### 🚀 一键启动（推荐）
```bash
# Windows PowerShell
.\scripts\setup.ps1

# Linux/macOS
chmod +x scripts/setup.sh
./scripts/setup.sh
```

### 手动安装
```bash
# 1. 安装根目录依赖
npm install

# 2. 安装前后端依赖
npm run setup

# 3. 配置环境变量
cp backend\.env.example backend\.env
cp frontend\.env.example frontend\.env

# 4. 启动开发服务器
npm run dev
```

### 访问应用
- 🌐 前端应用: http://localhost:5173
- 🔧 后端API: http://localhost:3000
- 📚 API文档: http://localhost:3000/api-docs
- 🏥 健康检查: http://localhost:3000/health

### 数据库配置（可选）
如需完整功能，请配置PostgreSQL数据库：

```bash
# 使用Docker启动PostgreSQL
docker run --name postgres-mutual-like \
  -e POSTGRES_PASSWORD=postgres123 \
  -e POSTGRES_DB=mutual_like_platform \
  -p 5432:5432 -d postgres:15

# 运行数据库迁移
cd backend
npm run db:migrate
npm run db:seed
```

### Docker部署
```bash
# 构建镜像
npm run docker:build

# 启动服务
npm run docker:up

# 停止服务
npm run docker:down
```

## 项目结构

```
mutual-like-platform/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── controllers/     # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript类型定义
│   ├── migrations/         # 数据库迁移
│   ├── seeds/             # 种子数据
│   └── tests/             # 测试文件
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── hooks/         # 自定义Hooks
│   │   ├── services/      # API服务
│   │   ├── store/         # 状态管理
│   │   ├── types/         # TypeScript类型
│   │   └── utils/         # 工具函数
│   └── public/            # 静态资源
├── docker-compose.yml      # Docker编排文件
└── docs/                  # 项目文档
```

## API文档

启动后端服务后，访问 `http://localhost:3000/api-docs` 查看Swagger API文档。

## 功能模块

### 用户系统
- [x] 用户注册/登录
- [x] 邮箱验证
- [x] 密码重置
- [x] 社交账号绑定

### 积分系统
- [x] 积分获取/消费
- [x] 积分流水记录
- [x] 积分统计

### 任务系统
- [x] 任务发布
- [x] 任务大厅
- [x] 任务执行
- [x] 自动验证

### 安全机制
- [x] JWT认证
- [x] 请求限流
- [x] IP限制
- [x] 防作弊检测

## 开发指南

### 代码规范
- 使用ESLint + Prettier进行代码格式化
- 遵循TypeScript严格模式
- 组件使用函数式组件 + Hooks
- API使用RESTful设计

### 测试
```bash
# 运行后端测试
cd backend && npm test

# 运行前端测试
cd frontend && npm test
```

### 部署
详见 [部署文档](docs/deployment.md)

## 许可证

MIT License
