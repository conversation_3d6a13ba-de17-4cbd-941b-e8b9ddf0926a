"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskExecutionModel = void 0;
const database_1 = require("@/config/database");
class TaskExecutionModel {
    static async create(executionData) {
        const [execution] = await (0, database_1.db)(this.tableName)
            .insert({
            ...executionData,
            execution_proof: JSON.stringify(executionData.execution_proof || {}),
            verification_result: JSON.stringify({}),
            status: 'pending',
            submitted_at: new Date(),
        })
            .returning('*');
        return execution;
    }
    static async findById(id) {
        const execution = await (0, database_1.db)(this.tableName).where({ id }).first();
        return execution || null;
    }
    static async findByExecutor(executorId, page = 1, limit = 20, status) {
        const offset = (page - 1) * limit;
        let query = (0, database_1.db)(this.tableName)
            .select('task_executions.*', 'tasks.title as task_title', 'tasks.reward_points')
            .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
            .where('task_executions.executor_id', executorId);
        if (status) {
            query = query.where('task_executions.status', status);
        }
        const countResult = await query.clone().count('* as count');
        const total = Number(countResult[0]?.count) || 0;
        const executions = await query
            .orderBy('task_executions.submitted_at', 'desc')
            .limit(limit)
            .offset(offset);
        const totalPages = Math.ceil(total / limit);
        return {
            executions,
            total,
            totalPages,
        };
    }
    static async findByTask(taskId, page = 1, limit = 20) {
        const offset = (page - 1) * limit;
        const countResult = await (0, database_1.db)(this.tableName)
            .where({ task_id: taskId })
            .count('* as count');
        const executions = await (0, database_1.db)(this.tableName)
            .select('task_executions.*', 'users.username as executor_username')
            .leftJoin('users', 'task_executions.executor_id', 'users.id')
            .where('task_executions.task_id', taskId)
            .orderBy('task_executions.submitted_at', 'desc')
            .limit(limit)
            .offset(offset);
        const total = Number(countResult[0]?.count) || 0;
        const totalPages = Math.ceil(total / limit);
        return {
            executions,
            total,
            totalPages,
        };
    }
    static async updateStatus(id, status, verificationResult, rejectionReason) {
        const updateData = {
            status,
            updated_at: new Date(),
        };
        if (status === 'verified' || status === 'failed') {
            updateData.verified_at = new Date();
        }
        if (verificationResult) {
            updateData.verification_result = JSON.stringify(verificationResult);
        }
        if (rejectionReason) {
            updateData.rejection_reason = rejectionReason;
        }
        const [execution] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .update(updateData)
            .returning('*');
        return execution || null;
    }
    static async hasExecuted(taskId, executorId) {
        const execution = await (0, database_1.db)(this.tableName)
            .where({ task_id: taskId, executor_id: executorId })
            .first();
        return !!execution;
    }
    static async findPendingVerifications(limit = 50) {
        return (0, database_1.db)(this.tableName)
            .select('task_executions.*', 'tasks.platform', 'tasks.task_type', 'tasks.video_url')
            .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
            .where('task_executions.status', 'pending')
            .where('task_executions.submitted_at', '<', new Date(Date.now() - 60000))
            .orderBy('task_executions.submitted_at', 'asc')
            .limit(limit);
    }
    static async getTodayExecutionCount(executorId) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const countResult = await (0, database_1.db)(this.tableName)
            .where('executor_id', executorId)
            .where('submitted_at', '>=', today)
            .where('submitted_at', '<', tomorrow)
            .count('* as count');
        return Number(countResult[0]?.count) || 0;
    }
    static async getTodayExecutionCountByIP(ipAddress) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const countResult = await (0, database_1.db)(this.tableName)
            .where('ip_address', ipAddress)
            .where('submitted_at', '>=', today)
            .where('submitted_at', '<', tomorrow)
            .count('* as count');
        return Number(countResult[0]?.count) || 0;
    }
    static async getExecutionStats(executorId) {
        const stats = await (0, database_1.db)(this.tableName)
            .select('status')
            .count('* as count')
            .where('executor_id', executorId)
            .groupBy('status');
        const result = {
            total: 0,
            pending: 0,
            completed: 0,
            verified: 0,
            failed: 0,
            rejected: 0,
            successRate: 0,
        };
        stats.forEach((stat) => {
            const count = Number(stat.count);
            result.total += count;
            result[stat.status] = count;
        });
        if (result.total > 0) {
            result.successRate = Math.round((result.verified / result.total) * 100);
        }
        return result;
    }
    static async updateExpiredPending() {
        const expiredTime = new Date(Date.now() - 30 * 60 * 1000);
        const updatedCount = await (0, database_1.db)(this.tableName)
            .where('status', 'pending')
            .where('submitted_at', '<', expiredTime)
            .update({
            status: 'failed',
            verification_result: JSON.stringify({
                success: false,
                error_message: '验证超时',
                verified_at: new Date(),
            }),
            verified_at: new Date(),
            updated_at: new Date(),
        });
        return updatedCount;
    }
    static async delete(id) {
        const deletedCount = await (0, database_1.db)(this.tableName).where({ id }).del();
        return deletedCount > 0;
    }
    static async getSystemExecutionStats() {
        const totalResult = await (0, database_1.db)(this.tableName)
            .count('* as count');
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const todayResult = await (0, database_1.db)(this.tableName)
            .where('submitted_at', '>=', today)
            .where('submitted_at', '<', tomorrow)
            .count('* as count');
        const verifiedResult = await (0, database_1.db)(this.tableName)
            .where('status', 'verified')
            .count('* as count');
        const platformStats = await (0, database_1.db)(this.tableName)
            .select('tasks.platform')
            .count('* as count')
            .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
            .groupBy('tasks.platform');
        const totalExecutions = Number(totalResult[0]?.count) || 0;
        const todayExecutions = Number(todayResult[0]?.count) || 0;
        const verifiedExecutions = Number(verifiedResult[0]?.count) || 0;
        const platformStatsObj = {};
        platformStats.forEach((stat) => {
            if (stat.platform) {
                platformStatsObj[stat.platform] = Number(stat.count);
            }
        });
        return {
            totalExecutions,
            todayExecutions,
            successRate: totalExecutions > 0 ? Math.round((verifiedExecutions / totalExecutions) * 100) : 0,
            platformStats: platformStatsObj,
        };
    }
}
exports.TaskExecutionModel = TaskExecutionModel;
TaskExecutionModel.tableName = 'task_executions';
//# sourceMappingURL=TaskExecution.js.map