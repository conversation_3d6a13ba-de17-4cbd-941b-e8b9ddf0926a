import { body, param, query, ValidationChain } from 'express-validator';

/**
 * 用户注册验证
 */
export const validateRegister: ValidationChain[] = [
  body('username')
    .isLength({ min: 3, max: 20 })
    .withMessage('用户名长度必须在3-20个字符之间')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名只能包含字母、数字和下划线'),
  
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6, max: 50 })
    .withMessage('密码长度必须在6-50个字符之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
];

/**
 * 用户登录验证
 */
export const validateLogin: ValidationChain[] = [
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  
  body('password')
    .notEmpty()
    .withMessage('密码不能为空'),
];

/**
 * 创建任务验证
 */
export const validateCreateTask: ValidationChain[] = [
  body('title')
    .isLength({ min: 5, max: 100 })
    .withMessage('任务标题长度必须在5-100个字符之间'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('任务描述不能超过500个字符'),
  
  body('video_url')
    .isURL()
    .withMessage('请输入有效的视频链接'),
  
  body('platform')
    .isIn(['douyin', 'kuaishou', 'xiaohongshu'])
    .withMessage('平台类型无效'),
  
  body('task_type')
    .isIn(['like', 'share', 'follow', 'comment'])
    .withMessage('任务类型无效'),
  
  body('reward_points')
    .isInt({ min: 1, max: 50 })
    .withMessage('奖励积分必须在1-50之间'),
  
  body('total_quota')
    .isInt({ min: 1, max: 1000 })
    .withMessage('任务名额必须在1-1000之间'),
  
  body('expires_in_days')
    .isInt({ min: 1, max: 30 })
    .withMessage('有效期必须在1-30天之间'),
];

/**
 * 更新任务验证
 */
export const validateUpdateTask: ValidationChain[] = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('任务ID必须是正整数'),
  
  body('title')
    .optional()
    .isLength({ min: 5, max: 100 })
    .withMessage('任务标题长度必须在5-100个字符之间'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('任务描述不能超过500个字符'),
  
  body('status')
    .optional()
    .isIn(['active', 'paused', 'completed', 'expired'])
    .withMessage('任务状态无效'),
];

/**
 * 分页验证
 */
export const validatePagination: ValidationChain[] = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

/**
 * 任务筛选验证
 */
export const validateTaskFilters: ValidationChain[] = [
  query('platform')
    .optional()
    .isIn(['douyin', 'kuaishou', 'xiaohongshu'])
    .withMessage('平台类型无效'),
  
  query('task_type')
    .optional()
    .isIn(['like', 'share', 'follow', 'comment'])
    .withMessage('任务类型无效'),
  
  query('min_points')
    .optional()
    .isInt({ min: 1 })
    .withMessage('最小积分必须是正整数'),
  
  query('max_points')
    .optional()
    .isInt({ min: 1 })
    .withMessage('最大积分必须是正整数'),
  
  query('search')
    .optional()
    .isLength({ max: 50 })
    .withMessage('搜索关键词不能超过50个字符'),
];

/**
 * ID参数验证
 */
export const validateId: ValidationChain[] = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),
];

/**
 * 密码重置验证
 */
export const validatePasswordReset: ValidationChain[] = [
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
];

/**
 * 新密码验证
 */
export const validateNewPassword: ValidationChain[] = [
  body('token')
    .notEmpty()
    .withMessage('重置令牌不能为空'),
  
  body('password')
    .isLength({ min: 6, max: 50 })
    .withMessage('密码长度必须在6-50个字符之间')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个小写字母、一个大写字母和一个数字'),
];

/**
 * 社交账号绑定验证
 */
export const validateSocialBinding: ValidationChain[] = [
  body('platform')
    .isIn(['douyin', 'kuaishou', 'xiaohongshu'])
    .withMessage('平台类型无效'),
  
  body('user_id')
    .notEmpty()
    .withMessage('用户ID不能为空'),
  
  body('username')
    .notEmpty()
    .withMessage('用户名不能为空'),
];

/**
 * 视频URL验证
 */
export const validateVideoUrl = (url: string): { isValid: boolean; platform?: string } => {
  const patterns = {
    douyin: /(?:douyin\.com|dy\.com)/i,
    kuaishou: /(?:kuaishou\.com|ks\.com)/i,
    xiaohongshu: /(?:xiaohongshu\.com|xhs\.com)/i,
  };

  for (const [platform, pattern] of Object.entries(patterns)) {
    if (pattern.test(url)) {
      return { isValid: true, platform };
    }
  }

  return { isValid: false };
};

/**
 * 邮箱验证
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 用户名验证
 */
export const isValidUsername = (username: string): boolean => {
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
};

/**
 * 密码强度验证
 */
export const isStrongPassword = (password: string): boolean => {
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,50}$/;
  return passwordRegex.test(password);
};
