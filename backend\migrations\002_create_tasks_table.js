/**
 * 创建任务表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('tasks', function(table) {
    table.increments('id').primary();
    table.integer('publisher_id').unsigned().notNullable();
    table.string('title', 200).notNullable();
    table.text('description');
    table.string('video_url', 500).notNullable();
    table.string('platform', 50).notNullable(); // douyin, kuaishou, xiaohongshu
    table.enum('task_type', ['like', 'share', 'follow', 'comment']).notNullable();
    table.integer('reward_points').notNullable();
    table.integer('total_quota').notNullable();
    table.integer('completed_count').defaultTo(0);
    table.enum('status', ['active', 'paused', 'completed', 'expired']).defaultTo('active');
    table.json('verification_rules').defaultTo('{}');
    table.timestamp('expires_at').notNullable();
    table.timestamps(true, true);
    
    // 外键约束
    table.foreign('publisher_id').references('id').inTable('users').onDelete('CASCADE');
    
    // 索引
    table.index(['publisher_id']);
    table.index(['platform']);
    table.index(['task_type']);
    table.index(['status']);
    table.index(['expires_at']);
    table.index(['created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('tasks');
};
