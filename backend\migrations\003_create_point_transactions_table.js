/**
 * 创建积分流水表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('point_transactions', function(table) {
    table.increments('id').primary();
    table.integer('user_id').unsigned().notNullable();
    table.integer('task_id').unsigned().nullable();
    table.integer('amount').notNullable(); // 正数为收入，负数为支出
    table.enum('transaction_type', ['reward', 'cost', 'bonus', 'refund', 'penalty']).notNullable();
    table.string('description', 500).notNullable();
    table.json('metadata').defaultTo('{}'); // 额外信息
    table.integer('balance_after').notNullable(); // 交易后余额
    table.timestamps(true, true);
    
    // 外键约束
    table.foreign('user_id').references('id').inTable('users').onDelete('CASCADE');
    table.foreign('task_id').references('id').inTable('tasks').onDelete('SET NULL');
    
    // 索引
    table.index(['user_id']);
    table.index(['task_id']);
    table.index(['transaction_type']);
    table.index(['created_at']);
    table.index(['user_id', 'created_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('point_transactions');
};
