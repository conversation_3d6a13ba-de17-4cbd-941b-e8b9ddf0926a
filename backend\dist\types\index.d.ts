export interface User {
    id: number;
    username: string;
    email: string;
    password_hash: string;
    points_balance: number;
    social_accounts: SocialAccounts;
    is_active: boolean;
    email_verification_token?: string;
    email_verified: boolean;
    password_reset_token?: string;
    password_reset_expires?: Date;
    last_login_ip?: string;
    last_login_at?: Date;
    created_at: Date;
    updated_at: Date;
}
export interface SocialAccounts {
    douyin?: {
        user_id: string;
        username: string;
        verified: boolean;
    };
    kuaishou?: {
        user_id: string;
        username: string;
        verified: boolean;
    };
    xiaohongshu?: {
        user_id: string;
        username: string;
        verified: boolean;
    };
}
export interface Task {
    id: number;
    publisher_id: number;
    title: string;
    description?: string;
    video_url: string;
    platform: Platform;
    task_type: TaskType;
    reward_points: number;
    total_quota: number;
    completed_count: number;
    status: TaskStatus;
    verification_rules: VerificationRules;
    expires_at: Date;
    created_at: Date;
    updated_at: Date;
}
export type Platform = 'douyin' | 'kuaishou' | 'xiaohong<PERSON>';
export type TaskType = 'like' | 'share' | 'follow' | 'comment';
export type TaskStatus = 'active' | 'paused' | 'completed' | 'expired';
export interface VerificationRules {
    auto_verify?: boolean;
    verification_delay?: number;
    required_proof?: string[];
}
export interface PointTransaction {
    id: number;
    user_id: number;
    task_id?: number;
    amount: number;
    transaction_type: TransactionType;
    description: string;
    metadata: Record<string, any>;
    balance_after: number;
    created_at: Date;
    updated_at: Date;
}
export type TransactionType = 'reward' | 'cost' | 'bonus' | 'refund' | 'penalty';
export interface TaskExecution {
    id: number;
    task_id: number;
    executor_id: number;
    status: ExecutionStatus;
    submitted_at: Date;
    verified_at?: Date;
    verification_result: VerificationResult;
    ip_address: string;
    user_agent?: string;
    execution_proof: ExecutionProof;
    rejection_reason?: string;
    created_at: Date;
    updated_at: Date;
}
export type ExecutionStatus = 'pending' | 'completed' | 'failed' | 'verified' | 'rejected';
export interface VerificationResult {
    success: boolean;
    verified_at?: Date;
    verification_method?: string;
    api_response?: any;
    error_message?: string;
}
export interface ExecutionProof {
    screenshot?: string;
    timestamp?: Date;
    platform_response?: any;
    additional_data?: Record<string, any>;
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    message?: string;
    error?: string;
    pagination?: PaginationInfo;
}
export interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
}
export interface LoginRequest {
    email: string;
    password: string;
}
export interface RegisterRequest {
    username: string;
    email: string;
    password: string;
}
export interface CreateTaskRequest {
    title: string;
    description?: string;
    video_url: string;
    platform: Platform;
    task_type: TaskType;
    reward_points: number;
    total_quota: number;
    expires_in_days: number;
    verification_rules?: VerificationRules;
}
export interface TaskFilters {
    platform?: Platform;
    task_type?: TaskType;
    min_points?: number;
    max_points?: number;
    status?: TaskStatus;
    search?: string;
}
export interface JwtPayload {
    userId: number;
    email: string;
    username: string;
    iat?: number;
    exp?: number;
}
export interface CountResult {
    count: string | number;
}
export interface SumResult {
    total: string | number;
}
declare global {
    namespace Express {
        interface Request {
            user?: JwtPayload;
        }
    }
}
//# sourceMappingURL=index.d.ts.map