import React, { useState } from 'react';
import { X, User, Settings, LogOut } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { useNavigate } from 'react-router-dom';

const DebugLogin: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { login, logout, isAuthenticated, user, tokens } = useAuthStore();
  const navigate = useNavigate();

  // 组件渲染日志
  React.useEffect(() => {
    console.log('🔧 DebugLogin 组件已渲染');
    console.log('🔧 当前认证状态:', { isAuthenticated, user: user?.username });
  }, [isAuthenticated, user]);

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      username: 'demo_user',
      email: '<EMAIL>',
      points_balance: 150,
      email_verified: true,
      social_accounts: {
        douyin: {
          user_id: 'demo123',
          username: 'demo_douyin',
          verified: true,
        }
      },
      created_at: new Date().toISOString(),
    },
    {
      id: 2,
      username: 'test_user',
      email: '<EMAIL>',
      points_balance: 250,
      email_verified: true,
      social_accounts: {},
      created_at: new Date().toISOString(),
    },
    {
      id: 3,
      username: 'admin_user',
      email: '<EMAIL>',
      points_balance: 1000,
      email_verified: true,
      social_accounts: {
        douyin: {
          user_id: 'admin123',
          username: 'admin_douyin',
          verified: true,
        },
        kuaishou: {
          user_id: 'admin456',
          username: 'admin_kuaishou',
          verified: true,
        }
      },
      created_at: new Date().toISOString(),
    }
  ];

  const handleDebugLogin = (user: any) => {
    console.log('🔧 调试登录开始:', user);

    // 生成模拟的JWT tokens
    const mockTokens = {
      accessToken: `mock_access_token_${user.id}_${Date.now()}`,
      refreshToken: `mock_refresh_token_${user.id}_${Date.now()}`,
    };

    console.log('🔧 生成的模拟tokens:', mockTokens);

    // 登录用户
    login(user, mockTokens);

    console.log('🔧 登录状态更新完成');

    // 关闭弹窗
    setIsOpen(false);

    // 使用更长的延迟，并且检查状态
    const checkAndNavigate = () => {
      const currentAuth = useAuthStore.getState().isAuthenticated;
      console.log('🔧 检查认证状态:', currentAuth);

      if (currentAuth) {
        console.log('🔧 认证状态确认，准备导航');
        navigate('/dashboard', { replace: true });
      } else {
        console.log('🔧 认证状态未更新，继续等待...');
        setTimeout(checkAndNavigate, 100);
      }
    };

    setTimeout(checkAndNavigate, 100);
  };

  const handleLogout = () => {
    logout();
    setIsOpen(false);
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-[9999]">
        <button
          onClick={() => {
            console.log('🔧 调试登录按钮被点击');
            setIsOpen(true);
          }}
          className="bg-red-500 hover:bg-red-600 text-white p-4 rounded-full shadow-xl transition-colors animate-pulse"
          title="调试登录"
          style={{ zIndex: 9999 }}
        >
          <Settings className="h-6 w-6" />
        </button>
        {/* 添加一个文字标签 */}
        <div className="absolute bottom-full right-0 mb-2 bg-black text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          调试登录
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">调试登录</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <p className="text-sm text-gray-600 mb-4">
          选择一个用户直接登录（无需数据库）
        </p>

        {/* 当前登录状态 */}
        <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-xs text-gray-600 mb-2">当前状态:</div>
          <div className="text-sm space-y-1">
            <div>认证状态: {isAuthenticated ? '✅ 已登录' : '❌ 未登录'}</div>
            {user && <div>用户: {user.username}</div>}
            {tokens && <div>Token: {tokens.accessToken.substring(0, 20)}...</div>}
            <div>LocalStorage: {localStorage.getItem('auth-storage') ? '✅ 有数据' : '❌ 无数据'}</div>
          </div>

          <div className="mt-2 space-y-1">
            <button
              onClick={() => {
                console.log('🔧 当前完整状态:', useAuthStore.getState());
                console.log('🔧 LocalStorage:', localStorage.getItem('auth-storage'));
              }}
              className="w-full text-xs bg-gray-200 hover:bg-gray-300 px-2 py-1 rounded"
            >
              打印完整状态
            </button>

            {isAuthenticated && (
              <button
                onClick={handleLogout}
                className="w-full text-red-600 hover:text-red-800 text-sm flex items-center justify-center"
              >
                <LogOut className="h-4 w-4 mr-1" />
                退出登录
              </button>
            )}
          </div>
        </div>

        <div className="space-y-3">
          {mockUsers.map((user) => (
            <button
              key={user.id}
              onClick={() => handleDebugLogin(user)}
              className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center">
                <User className="h-8 w-8 text-gray-400 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">{user.username}</div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                  <div className="text-xs text-green-600">积分: {user.points_balance}</div>
                </div>
              </div>
            </button>
          ))}
        </div>

        {/* 强制导航按钮 */}
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="space-y-2">
            <button
              onClick={() => {
                console.log('🔧 简单登录测试');

                // 先登录一个用户
                const mockUser = {
                  id: 1,
                  username: 'demo_user',
                  email: '<EMAIL>',
                  points_balance: 150,
                  email_verified: true,
                  social_accounts: {},
                  created_at: new Date().toISOString(),
                };

                const mockTokens = {
                  accessToken: `mock_access_token_1_${Date.now()}`,
                  refreshToken: `mock_refresh_token_1_${Date.now()}`,
                };

                login(mockUser, mockTokens);
                console.log('🔧 登录完成，直接跳转');

                setIsOpen(false);

                // 直接使用 window.location 跳转，绕过所有React Router逻辑
                window.location.href = '/dashboard';
              }}
              className="w-full p-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm"
            >
              登录并跳转到首页
            </button>

            <button
              onClick={() => {
                console.log('🔧 直接使用 window.location 跳转到首页');
                window.location.href = '/dashboard';
              }}
              className="w-full p-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors text-sm"
            >
              直接跳转到首页
            </button>

            <button
              onClick={() => {
                console.log('🔧 跳转到根路径');
                window.location.href = '/';
              }}
              className="w-full p-2 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors text-sm"
            >
              跳转到根路径 (/)
            </button>
          </div>
        </div>

        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-xs text-yellow-800">
            ⚠️ 这是调试功能，仅用于开发测试。生产环境请移除此组件。
          </p>
        </div>
      </div>
    </div>
  );
};

export default DebugLogin;
