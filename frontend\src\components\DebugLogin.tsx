import React, { useState } from 'react';
import { X, User, Settings, LogOut } from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { useNavigate } from 'react-router-dom';

const DebugLogin: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { login, logout, isAuthenticated, user, tokens } = useAuthStore();
  const navigate = useNavigate();

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      username: 'demo_user',
      email: '<EMAIL>',
      points_balance: 150,
      email_verified: true,
      social_accounts: {
        douyin: {
          user_id: 'demo123',
          username: 'demo_douyin',
          verified: true,
        }
      },
      created_at: new Date().toISOString(),
    },
    {
      id: 2,
      username: 'test_user',
      email: '<EMAIL>',
      points_balance: 250,
      email_verified: true,
      social_accounts: {},
      created_at: new Date().toISOString(),
    },
    {
      id: 3,
      username: 'admin_user',
      email: '<EMAIL>',
      points_balance: 1000,
      email_verified: true,
      social_accounts: {
        douyin: {
          user_id: 'admin123',
          username: 'admin_douyin',
          verified: true,
        },
        kuaishou: {
          user_id: 'admin456',
          username: 'admin_kuaishou',
          verified: true,
        }
      },
      created_at: new Date().toISOString(),
    }
  ];

  const handleDebugLogin = (user: any) => {
    console.log('🔧 调试登录开始:', user);

    // 生成模拟的JWT tokens
    const mockTokens = {
      accessToken: `mock_access_token_${user.id}_${Date.now()}`,
      refreshToken: `mock_refresh_token_${user.id}_${Date.now()}`,
    };

    console.log('🔧 生成的模拟tokens:', mockTokens);

    // 登录用户
    login(user, mockTokens);

    console.log('🔧 登录状态更新完成，当前认证状态:', isAuthenticated);

    // 关闭弹窗
    setIsOpen(false);

    // 延迟导航，确保状态更新完成
    setTimeout(() => {
      console.log('🔧 准备导航到 /dashboard');
      navigate('/dashboard');
    }, 100);
  };

  const handleLogout = () => {
    logout();
    setIsOpen(false);
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsOpen(true)}
          className="bg-red-500 hover:bg-red-600 text-white p-3 rounded-full shadow-lg transition-colors"
          title="调试登录"
        >
          <Settings className="h-5 w-5" />
        </button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">调试登录</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <p className="text-sm text-gray-600 mb-4">
          选择一个用户直接登录（无需数据库）
        </p>

        {/* 当前登录状态 */}
        <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <div className="text-xs text-gray-600 mb-2">当前状态:</div>
          <div className="text-sm">
            <div>认证状态: {isAuthenticated ? '✅ 已登录' : '❌ 未登录'}</div>
            {user && <div>用户: {user.username}</div>}
            {tokens && <div>Token: {tokens.accessToken.substring(0, 20)}...</div>}
          </div>
          {isAuthenticated && (
            <button
              onClick={handleLogout}
              className="mt-2 text-red-600 hover:text-red-800 text-sm flex items-center"
            >
              <LogOut className="h-4 w-4 mr-1" />
              退出登录
            </button>
          )}
        </div>

        <div className="space-y-3">
          {mockUsers.map((user) => (
            <button
              key={user.id}
              onClick={() => handleDebugLogin(user)}
              className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center">
                <User className="h-8 w-8 text-gray-400 mr-3" />
                <div>
                  <div className="font-medium text-gray-900">{user.username}</div>
                  <div className="text-sm text-gray-500">{user.email}</div>
                  <div className="text-xs text-green-600">积分: {user.points_balance}</div>
                </div>
              </div>
            </button>
          ))}
        </div>

        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-xs text-yellow-800">
            ⚠️ 这是调试功能，仅用于开发测试。生产环境请移除此组件。
          </p>
        </div>
      </div>
    </div>
  );
};

export default DebugLogin;
