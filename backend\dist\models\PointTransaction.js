"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PointTransactionModel = void 0;
const database_1 = require("@/config/database");
class PointTransactionModel {
    static async create(transactionData) {
        const [transaction] = await (0, database_1.db)(this.tableName)
            .insert({
            ...transactionData,
            metadata: JSON.stringify(transactionData.metadata || {}),
        })
            .returning('*');
        return transaction;
    }
    static async findByUser(userId, page = 1, limit = 20, transactionType) {
        const offset = (page - 1) * limit;
        let query = (0, database_1.db)(this.tableName)
            .select('point_transactions.*', 'tasks.title as task_title')
            .leftJoin('tasks', 'point_transactions.task_id', 'tasks.id')
            .where('point_transactions.user_id', userId);
        if (transactionType) {
            query = query.where('point_transactions.transaction_type', transactionType);
        }
        const countResult = await query.clone().count('* as count');
        const total = Number(countResult[0]?.count) || 0;
        const transactions = await query
            .orderBy('point_transactions.created_at', 'desc')
            .limit(limit)
            .offset(offset);
        const totalPages = Math.ceil(total / limit);
        return {
            transactions,
            total,
            totalPages,
        };
    }
    static async getTodayStats(userId) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        const earnedResult = await (0, database_1.db)(this.tableName)
            .where('user_id', userId)
            .where('amount', '>', 0)
            .where('created_at', '>=', today)
            .where('created_at', '<', tomorrow)
            .sum('amount as total');
        const spentResult = await (0, database_1.db)(this.tableName)
            .where('user_id', userId)
            .where('amount', '<', 0)
            .where('created_at', '>=', today)
            .where('created_at', '<', tomorrow)
            .sum('amount as total');
        const earned = Number(earnedResult[0]?.total) || 0;
        const spent = Math.abs(Number(spentResult[0]?.total)) || 0;
        return {
            earned,
            spent,
            net: earned - spent,
        };
    }
    static async getUserPointsStats(userId) {
        const earnedResult = await (0, database_1.db)(this.tableName)
            .where('user_id', userId)
            .where('amount', '>', 0)
            .sum('amount as total');
        const spentResult = await (0, database_1.db)(this.tableName)
            .where('user_id', userId)
            .where('amount', '<', 0)
            .sum('amount as total');
        const countResult = await (0, database_1.db)(this.tableName)
            .where('user_id', userId)
            .count('* as count');
        const balanceResult = await (0, database_1.db)('users')
            .where('id', userId)
            .select('points_balance')
            .first();
        return {
            totalEarned: Number(earnedResult[0]?.total) || 0,
            totalSpent: Math.abs(Number(spentResult[0]?.total)) || 0,
            currentBalance: balanceResult?.points_balance || 0,
            transactionCount: Number(countResult[0]?.count) || 0,
        };
    }
    static async executeTransaction(userId, amount, transactionType, description, taskId, metadata) {
        return database_1.db.transaction(async (trx) => {
            const user = await trx('users')
                .where('id', userId)
                .select('points_balance')
                .first();
            if (!user) {
                throw new Error('用户不存在');
            }
            const currentBalance = user.points_balance;
            const newBalance = currentBalance + amount;
            if (amount < 0 && newBalance < 0) {
                throw new Error('积分余额不足');
            }
            await trx('users')
                .where('id', userId)
                .update({
                points_balance: newBalance,
                updated_at: new Date(),
            });
            const [transaction] = await trx(this.tableName)
                .insert({
                user_id: userId,
                task_id: taskId,
                amount,
                transaction_type: transactionType,
                description,
                metadata: JSON.stringify(metadata || {}),
                balance_after: newBalance,
            })
                .returning('*');
            return transaction;
        });
    }
    static async getSystemStats() {
        const transactionCount = await (0, database_1.db)(this.tableName)
            .count('* as count');
        const pointsIssued = await (0, database_1.db)(this.tableName)
            .where('amount', '>', 0)
            .sum('amount as total');
        const pointsSpent = await (0, database_1.db)(this.tableName)
            .where('amount', '<', 0)
            .sum('amount as total');
        const activeUsers = await (0, database_1.db)(this.tableName)
            .distinct('user_id')
            .count('user_id as count');
        return {
            totalTransactions: Number(transactionCount[0]?.count) || 0,
            totalPointsIssued: Number(pointsIssued[0]?.total) || 0,
            totalPointsSpent: Math.abs(Number(pointsSpent[0]?.total)) || 0,
            activeUsers: Number(activeUsers[0]?.count) || 0,
        };
    }
    static async findByDateRange(userId, startDate, endDate) {
        return (0, database_1.db)(this.tableName)
            .select('point_transactions.*', 'tasks.title as task_title')
            .leftJoin('tasks', 'point_transactions.task_id', 'tasks.id')
            .where('point_transactions.user_id', userId)
            .where('point_transactions.created_at', '>=', startDate)
            .where('point_transactions.created_at', '<=', endDate)
            .orderBy('point_transactions.created_at', 'desc');
    }
    static async findByTask(taskId) {
        return (0, database_1.db)(this.tableName)
            .select('point_transactions.*', 'users.username')
            .leftJoin('users', 'point_transactions.user_id', 'users.id')
            .where('point_transactions.task_id', taskId)
            .orderBy('point_transactions.created_at', 'desc');
    }
}
exports.PointTransactionModel = PointTransactionModel;
PointTransactionModel.tableName = 'point_transactions';
//# sourceMappingURL=PointTransaction.js.map