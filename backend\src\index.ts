import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import { config } from 'dotenv';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

// 导入配置和中间件
import { testConnection } from '@/config/database';
import { globalErrorHandler, notFoundHandler } from '@/middleware/errorHandler';
import { generalLimiter } from '@/middleware/rateLimiter';
import logger from '@/utils/logger';

// 导入路由
import authRoutes from '@/routes/auth';
import demoRoutes from '@/routes/demo';

// 加载环境变量
config();

const app = express();
const PORT = process.env['PORT'] || 3000;
const HOST = process.env['HOST'] || 'localhost';

// Swagger配置
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: '互助点赞平台 API',
      version: '1.0.0',
      description: '基于积分的社交媒体互助系统 API 文档',
    },
    servers: [
      {
        url: `http://${HOST}:${PORT}/api`,
        description: '开发服务器',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
  },
  apis: ['./src/routes/*.ts'], // API文档路径
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// 基础中间件
app.use(helmet()); // 安全头
app.use(compression()); // 压缩响应
app.use(morgan('combined', { stream: { write: (message) => logger.http(message.trim()) } })); // 日志

// CORS配置
app.use(cors({
  origin: process.env['CORS_ORIGIN'] || 'http://localhost:5173',
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
}));

// 解析请求体
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 限流
app.use(generalLimiter);

// 健康检查
app.get('/health', (_req, res) => {
  res.json({
    success: true,
    message: '服务运行正常',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// API文档
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec));

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/demo', demoRoutes);

// 404处理
app.use(notFoundHandler);

// 全局错误处理
app.use(globalErrorHandler);

// 启动服务器
const startServer = async () => {
  try {
    // 测试数据库连接（但不阻止启动）
    const dbConnected = await testConnection();

    // 启动HTTP服务器
    app.listen(PORT, () => {
      logger.info(`🚀 服务器启动成功`);
      logger.info(`📍 地址: http://${HOST}:${PORT}`);
      logger.info(`📚 API文档: http://${HOST}:${PORT}/api-docs`);
      logger.info(`🏥 健康检查: http://${HOST}:${PORT}/health`);
      logger.info(`🌍 环境: ${process.env['NODE_ENV'] || 'development'}`);

      if (!dbConnected) {
        logger.warn('⚠️  数据库未连接，某些功能可能无法正常工作');
        logger.info('💡 请配置并启动PostgreSQL数据库以获得完整功能');
      }
    });
  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
};

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，正在关闭服务器...');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，正在关闭服务器...');
  process.exit(0);
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, _promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
startServer();

export default app;
