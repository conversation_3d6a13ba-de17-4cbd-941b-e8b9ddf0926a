{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiter.ts"], "names": [], "mappings": ";;;;;;AAAA,4EAA2C;AAO9B,QAAA,cAAc,GAAG,IAAA,4BAAS,EAAC;IACtC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,GAAG;IACR,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,cAAc;KACtB;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,YAAY,GAAG,IAAA,4BAAS,EAAC;IACpC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,mBAAmB;KAC3B;IACD,sBAAsB,EAAE,IAAI;IAC5B,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,eAAe,GAAG,IAAA,4BAAS,EAAC;IACvC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,gBAAgB;KACxB;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,oBAAoB,GAAG,IAAA,4BAAS,EAAC;IAC5C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,kBAAkB;KAC1B;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,mBAAmB,GAAG,IAAA,4BAAS,EAAC;IAC3C,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,EAAE;IACP,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,kBAAkB;KAC1B;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,oBAAoB,GAAG,IAAA,4BAAS,EAAC;IAC5C,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,GAAG,EAAE,EAAE;IACP,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,gBAAgB;KACxB;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,UAAU,GAAG,IAAA,4BAAS,EAAC;IAClC,QAAQ,EAAE,EAAE,GAAG,IAAI;IACnB,GAAG,EAAE,EAAE;IACP,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,iBAAiB;KACzB;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKU,QAAA,aAAa,GAAG,IAAA,4BAAS,EAAC;IACrC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;IACxB,GAAG,EAAE,CAAC;IACN,OAAO,EAAE;QACP,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,gBAAgB;KACxB;IACD,eAAe,EAAE,IAAI;IACrB,aAAa,EAAE,KAAK;CACrB,CAAC,CAAC;AAKI,MAAM,sBAAsB,GAAG,CACpC,QAAgB,EAChB,GAAW,EACX,OAAe,EACf,EAAE;IACF,MAAM,KAAK,GAAG,IAAI,GAAG,EAAgD,CAAC;IAEtE,OAAO,CAAC,GAAY,EAAE,GAA0B,EAAE,IAAkB,EAAE,EAAE;QACtE,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,EAAE,CAAC;QACtD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,GAAG,GAAG,GAAG,MAAM,EAAE,CAAC;QAGxB,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YAC7C,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;QAGD,MAAM,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC;QAGzE,IAAI,MAAM,CAAC,KAAK,IAAI,GAAG,EAAE,CAAC;YACxB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,OAAO;aACf,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAEvB,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AApCW,QAAA,sBAAsB,0BAoCjC;AAKW,QAAA,wBAAwB,GAAG,IAAA,8BAAsB,EAC5D,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,EAAE,EACF,oBAAoB,CACrB,CAAC;AAKW,QAAA,uBAAuB,GAAG,IAAA,8BAAsB,EAC3D,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EACnB,EAAE,EACF,oBAAoB,CACrB,CAAC"}