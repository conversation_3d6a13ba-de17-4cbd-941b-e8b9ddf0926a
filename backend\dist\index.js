"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const morgan_1 = __importDefault(require("morgan"));
const compression_1 = __importDefault(require("compression"));
const dotenv_1 = require("dotenv");
const swagger_jsdoc_1 = __importDefault(require("swagger-jsdoc"));
const swagger_ui_express_1 = __importDefault(require("swagger-ui-express"));
const database_1 = require("@/config/database");
const errorHandler_1 = require("@/middleware/errorHandler");
const rateLimiter_1 = require("@/middleware/rateLimiter");
const logger_1 = __importDefault(require("@/utils/logger"));
const auth_1 = __importDefault(require("@/routes/auth"));
(0, dotenv_1.config)();
const app = (0, express_1.default)();
const PORT = process.env['PORT'] || 3000;
const HOST = process.env['HOST'] || 'localhost';
const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: '互助点赞平台 API',
            version: '1.0.0',
            description: '基于积分的社交媒体互助系统 API 文档',
        },
        servers: [
            {
                url: `http://${HOST}:${PORT}/api`,
                description: '开发服务器',
            },
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                },
            },
        },
    },
    apis: ['./src/routes/*.ts'],
};
const swaggerSpec = (0, swagger_jsdoc_1.default)(swaggerOptions);
app.use((0, helmet_1.default)());
app.use((0, compression_1.default)());
app.use((0, morgan_1.default)('combined', { stream: { write: (message) => logger_1.default.http(message.trim()) } }));
app.use((0, cors_1.default)({
    origin: process.env['CORS_ORIGIN'] || 'http://localhost:5173',
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
}));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use(rateLimiter_1.generalLimiter);
app.get('/health', (_req, res) => {
    res.json({
        success: true,
        message: '服务运行正常',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
    });
});
app.use('/api-docs', swagger_ui_express_1.default.serve, swagger_ui_express_1.default.setup(swaggerSpec));
app.use('/api/auth', auth_1.default);
app.use(errorHandler_1.notFoundHandler);
app.use(errorHandler_1.globalErrorHandler);
const startServer = async () => {
    try {
        const dbConnected = await (0, database_1.testConnection)();
        if (!dbConnected) {
            logger_1.default.error('数据库连接失败，服务器启动中止');
            process.exit(1);
        }
        app.listen(PORT, () => {
            logger_1.default.info(`🚀 服务器启动成功`);
            logger_1.default.info(`📍 地址: http://${HOST}:${PORT}`);
            logger_1.default.info(`📚 API文档: http://${HOST}:${PORT}/api-docs`);
            logger_1.default.info(`🏥 健康检查: http://${HOST}:${PORT}/health`);
            logger_1.default.info(`🌍 环境: ${process.env['NODE_ENV'] || 'development'}`);
        });
    }
    catch (error) {
        logger_1.default.error('服务器启动失败:', error);
        process.exit(1);
    }
};
process.on('SIGTERM', () => {
    logger_1.default.info('收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});
process.on('SIGINT', () => {
    logger_1.default.info('收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});
process.on('uncaughtException', (error) => {
    logger_1.default.error('未捕获的异常:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, _promise) => {
    logger_1.default.error('未处理的Promise拒绝:', reason);
    process.exit(1);
});
startServer();
exports.default = app;
//# sourceMappingURL=index.js.map