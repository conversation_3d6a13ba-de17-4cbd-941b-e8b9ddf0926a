{"name": "mutual-like-platform", "version": "1.0.0", "description": "互助点赞平台 - 基于积分的社交媒体互助系统", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup": "npm run setup:backend && npm run setup:frontend", "setup:backend": "cd backend && npm install", "setup:frontend": "cd frontend && npm install"}, "keywords": ["social-media", "like-platform", "points-system", "mutual-help"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}