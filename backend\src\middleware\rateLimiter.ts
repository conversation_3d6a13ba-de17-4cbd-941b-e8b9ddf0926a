import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';
import { ApiResponse } from '@/types';

/**
 * 通用限流中间件
 */
export const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 每个IP最多100个请求
  message: {
    success: false,
    error: '请求过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 登录限流中间件
 */
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 每个IP最多5次登录尝试
  message: {
    success: false,
    error: '登录尝试次数过多，请15分钟后再试',
  },
  skipSuccessfulRequests: true,
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 注册限流中间件
 */
export const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每个IP最多3次注册
  message: {
    success: false,
    error: '注册次数过多，请1小时后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 密码重置限流中间件
 */
export const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每个IP最多3次密码重置请求
  message: {
    success: false,
    error: '密码重置请求过多，请1小时后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 任务创建限流中间件
 */
export const taskCreationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 每个IP最多创建10个任务
  message: {
    success: false,
    error: '任务创建过于频繁，请1小时后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 任务执行限流中间件
 */
export const taskExecutionLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 每个IP每分钟最多执行10个任务
  message: {
    success: false,
    error: '任务执行过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * API调用限流中间件
 */
export const apiLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 每个IP每分钟最多60个API请求
  message: {
    success: false,
    error: 'API调用过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 严格限流中间件（用于敏感操作）
 */
export const strictLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 5, // 每个IP最多5次请求
  message: {
    success: false,
    error: '操作过于频繁，请1小时后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * 基于用户的限流中间件工厂
 */
export const createUserBasedLimiter = (
  windowMs: number,
  max: number,
  message: string
) => {
  const store = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response<ApiResponse>, next: NextFunction) => {
    const userId = req.user?.userId?.toString() || req.ip;
    const now = Date.now();
    const key = `${userId}`;

    // 清理过期记录
    const userRecord = store.get(key);
    if (userRecord && now > userRecord.resetTime) {
      store.delete(key);
    }

    // 获取或创建用户记录
    const record = store.get(key) || { count: 0, resetTime: now + windowMs };

    // 检查是否超过限制
    if (record.count >= max) {
      res.status(429).json({
        success: false,
        error: message,
      });
      return;
    }

    // 增加计数
    record.count++;
    store.set(key, record);

    next();
  };
};

/**
 * 用户任务执行限流（每日限制）
 */
export const userTaskExecutionLimiter = createUserBasedLimiter(
  24 * 60 * 60 * 1000, // 24小时
  50, // 每用户每天最多执行50个任务
  '今日任务执行次数已达上限，请明天再试'
);

/**
 * 用户任务发布限流（每日限制）
 */
export const userTaskCreationLimiter = createUserBasedLimiter(
  24 * 60 * 60 * 1000, // 24小时
  10, // 每用户每天最多发布10个任务
  '今日任务发布次数已达上限，请明天再试'
);
