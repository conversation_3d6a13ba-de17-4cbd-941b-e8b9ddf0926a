import { db } from '@/config/database';
import { TaskExecution, ExecutionStatus } from '@/types';

export class TaskExecutionModel {
  private static tableName = 'task_executions';

  /**
   * 创建任务执行记录
   */
  static async create(executionData: {
    task_id: number;
    executor_id: number;
    ip_address: string;
    user_agent?: string;
    execution_proof?: Record<string, any>;
  }): Promise<TaskExecution> {
    const [execution] = await db(this.tableName)
      .insert({
        ...executionData,
        execution_proof: JSON.stringify(executionData.execution_proof || {}),
        verification_result: JSON.stringify({}),
        status: 'pending',
        submitted_at: new Date(),
      })
      .returning('*');

    return execution;
  }

  /**
   * 根据ID查找执行记录
   */
  static async findById(id: number): Promise<TaskExecution | null> {
    const execution = await db(this.tableName).where({ id }).first();
    return execution || null;
  }

  /**
   * 获取用户的任务执行记录
   */
  static async findByExecutor(
    executorId: number,
    page: number = 1,
    limit: number = 20,
    status?: ExecutionStatus
  ): Promise<{
    executions: TaskExecution[];
    total: number;
    totalPages: number;
  }> {
    const offset = (page - 1) * limit;

    let query = db(this.tableName)
      .select('task_executions.*', 'tasks.title as task_title', 'tasks.reward_points')
      .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
      .where('task_executions.executor_id', executorId);

    if (status) {
      query = query.where('task_executions.status', status);
    }

    const [{ count }] = await query.clone().count('* as count');
    const total = Number(count);

    const executions = await query
      .orderBy('task_executions.submitted_at', 'desc')
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      executions,
      total,
      totalPages,
    };
  }

  /**
   * 获取任务的执行记录
   */
  static async findByTask(
    taskId: number,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    executions: TaskExecution[];
    total: number;
    totalPages: number;
  }> {
    const offset = (page - 1) * limit;

    const [{ count }] = await db(this.tableName)
      .where({ task_id: taskId })
      .count('* as count');

    const executions = await db(this.tableName)
      .select('task_executions.*', 'users.username as executor_username')
      .leftJoin('users', 'task_executions.executor_id', 'users.id')
      .where('task_executions.task_id', taskId)
      .orderBy('task_executions.submitted_at', 'desc')
      .limit(limit)
      .offset(offset);

    const total = Number(count);
    const totalPages = Math.ceil(total / limit);

    return {
      executions,
      total,
      totalPages,
    };
  }

  /**
   * 更新执行状态
   */
  static async updateStatus(
    id: number,
    status: ExecutionStatus,
    verificationResult?: Record<string, any>,
    rejectionReason?: string
  ): Promise<TaskExecution | null> {
    const updateData: any = {
      status,
      updated_at: new Date(),
    };

    if (status === 'verified' || status === 'failed') {
      updateData.verified_at = new Date();
    }

    if (verificationResult) {
      updateData.verification_result = JSON.stringify(verificationResult);
    }

    if (rejectionReason) {
      updateData.rejection_reason = rejectionReason;
    }

    const [execution] = await db(this.tableName)
      .where({ id })
      .update(updateData)
      .returning('*');

    return execution || null;
  }

  /**
   * 检查用户是否已执行过任务
   */
  static async hasExecuted(taskId: number, executorId: number): Promise<boolean> {
    const execution = await db(this.tableName)
      .where({ task_id: taskId, executor_id: executorId })
      .first();

    return !!execution;
  }

  /**
   * 获取待验证的执行记录
   */
  static async findPendingVerifications(limit: number = 50): Promise<TaskExecution[]> {
    return db(this.tableName)
      .select('task_executions.*', 'tasks.platform', 'tasks.task_type', 'tasks.video_url')
      .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
      .where('task_executions.status', 'pending')
      .where('task_executions.submitted_at', '<', new Date(Date.now() - 60000)) // 1分钟前提交的
      .orderBy('task_executions.submitted_at', 'asc')
      .limit(limit);
  }

  /**
   * 获取用户今日执行统计
   */
  static async getTodayExecutionCount(executorId: number): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [{ count }] = await db(this.tableName)
      .where('executor_id', executorId)
      .where('submitted_at', '>=', today)
      .where('submitted_at', '<', tomorrow)
      .count('* as count');

    return Number(count) || 0;
  }

  /**
   * 获取IP今日执行统计
   */
  static async getTodayExecutionCountByIP(ipAddress: string): Promise<number> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [{ count }] = await db(this.tableName)
      .where('ip_address', ipAddress)
      .where('submitted_at', '>=', today)
      .where('submitted_at', '<', tomorrow)
      .count('* as count');

    return Number(count) || 0;
  }

  /**
   * 获取用户执行统计
   */
  static async getExecutionStats(executorId: number): Promise<{
    total: number;
    pending: number;
    completed: number;
    verified: number;
    failed: number;
    rejected: number;
    successRate: number;
  }> {
    const stats = await db(this.tableName)
      .select('status')
      .count('* as count')
      .where('executor_id', executorId)
      .groupBy('status');

    const result = {
      total: 0,
      pending: 0,
      completed: 0,
      verified: 0,
      failed: 0,
      rejected: 0,
      successRate: 0,
    };

    stats.forEach((stat) => {
      const count = Number(stat.count);
      result.total += count;
      result[stat.status as keyof typeof result] = count;
    });

    if (result.total > 0) {
      result.successRate = Math.round((result.verified / result.total) * 100);
    }

    return result;
  }

  /**
   * 批量更新过期的待验证记录
   */
  static async updateExpiredPending(): Promise<number> {
    const expiredTime = new Date(Date.now() - 30 * 60 * 1000); // 30分钟前

    const updatedCount = await db(this.tableName)
      .where('status', 'pending')
      .where('submitted_at', '<', expiredTime)
      .update({
        status: 'failed',
        verification_result: JSON.stringify({
          success: false,
          error_message: '验证超时',
          verified_at: new Date(),
        }),
        verified_at: new Date(),
        updated_at: new Date(),
      });

    return updatedCount;
  }

  /**
   * 删除执行记录
   */
  static async delete(id: number): Promise<boolean> {
    const deletedCount = await db(this.tableName).where({ id }).del();
    return deletedCount > 0;
  }

  /**
   * 获取系统执行统计
   */
  static async getSystemExecutionStats(): Promise<{
    totalExecutions: number;
    todayExecutions: number;
    successRate: number;
    platformStats: Record<string, number>;
  }> {
    const [totalResult] = await db(this.tableName)
      .count('* as count');

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [todayResult] = await db(this.tableName)
      .where('submitted_at', '>=', today)
      .where('submitted_at', '<', tomorrow)
      .count('* as count');

    const [verifiedResult] = await db(this.tableName)
      .where('status', 'verified')
      .count('* as count');

    const platformStats = await db(this.tableName)
      .select('tasks.platform')
      .count('* as count')
      .leftJoin('tasks', 'task_executions.task_id', 'tasks.id')
      .groupBy('tasks.platform');

    const totalExecutions = Number(totalResult.count) || 0;
    const todayExecutions = Number(todayResult.count) || 0;
    const verifiedExecutions = Number(verifiedResult.count) || 0;

    const platformStatsObj: Record<string, number> = {};
    platformStats.forEach((stat) => {
      if (stat.platform) {
        platformStatsObj[stat.platform] = Number(stat.count);
      }
    });

    return {
      totalExecutions,
      todayExecutions,
      successRate: totalExecutions > 0 ? Math.round((verifiedExecutions / totalExecutions) * 100) : 0,
      platformStats: platformStatsObj,
    };
  }
}
