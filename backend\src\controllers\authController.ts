import { Request, Response } from 'express';
import { UserModel } from '@/models/User';
import { PointTransactionModel } from '@/models/PointTransaction';
import { generateAccessToken, generateRefreshToken } from '@/utils/jwt';
import { AppError, asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse, LoginRequest, RegisterRequest } from '@/types';
import logger from '@/utils/logger';

/**
 * 用户注册
 */
export const register = asyncHandler(async (
  req: Request<{}, ApiResponse, RegisterRequest>,
  res: Response<ApiResponse>
): Promise<void> => {
  const { username, email, password } = req.body;

  // 检查用户名是否已存在
  const existingUsername = await UserModel.isUsernameExists(username);
  if (existingUsername) {
    throw new AppError('用户名已存在', 409);
  }

  // 检查邮箱是否已存在
  const existingEmail = await UserModel.isEmailExists(email);
  if (existingEmail) {
    throw new AppError('邮箱已被注册', 409);
  }

  // 创建用户
  const user = await UserModel.create({
    username,
    email,
    password,
    points_balance: 100, // 初始积分
  });

  // 创建初始积分记录
  await PointTransactionModel.create({
    user_id: user.id,
    amount: 100,
    transaction_type: 'bonus',
    description: '新用户注册奖励',
    balance_after: 100,
  });

  // 生成令牌
  const accessToken = generateAccessToken({
    userId: user.id,
    email: user.email,
    username: user.username,
  });

  const refreshToken = generateRefreshToken({
    userId: user.id,
    email: user.email,
    username: user.username,
  });

  logger.info(`新用户注册: ${username} (${email})`);

  res.status(201).json({
    success: true,
    message: '注册成功',
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        points_balance: user.points_balance,
        email_verified: user.email_verified,
      },
      tokens: {
        accessToken,
        refreshToken,
      },
    },
  });
});

/**
 * 用户登录
 */
export const login = asyncHandler(async (
  req: Request<{}, ApiResponse, LoginRequest>,
  res: Response<ApiResponse>
): Promise<void> => {
  const { email, password } = req.body;

  // 查找用户
  const user = await UserModel.findByEmail(email);
  if (!user) {
    throw new AppError('邮箱或密码错误', 401);
  }

  // 检查账号状态
  if (!user.is_active) {
    throw new AppError('账号已被禁用', 403);
  }

  // 验证密码
  const isPasswordValid = await UserModel.verifyPassword(password, user.password_hash);
  if (!isPasswordValid) {
    throw new AppError('邮箱或密码错误', 401);
  }

  // 更新最后登录信息
  await UserModel.updateLastLogin(user.id, req.ip);

  // 生成令牌
  const accessToken = generateAccessToken({
    userId: user.id,
    email: user.email,
    username: user.username,
  });

  const refreshToken = generateRefreshToken({
    userId: user.id,
    email: user.email,
    username: user.username,
  });

  logger.info(`用户登录: ${user.username} (${user.email}) from ${req.ip}`);

  res.json({
    success: true,
    message: '登录成功',
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        points_balance: user.points_balance,
        email_verified: user.email_verified,
        social_accounts: user.social_accounts,
      },
      tokens: {
        accessToken,
        refreshToken,
      },
    },
  });
});

/**
 * 获取当前用户信息
 */
export const getCurrentUser = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  if (!req.user) {
    throw new AppError('未登录', 401);
  }

  const user = await UserModel.findById(req.user.userId);
  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  // 获取用户统计信息
  const stats = await UserModel.getUserStats(user.id);

  res.json({
    success: true,
    data: {
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        points_balance: user.points_balance,
        email_verified: user.email_verified,
        social_accounts: user.social_accounts,
        created_at: user.created_at,
        last_login_at: user.last_login_at,
      },
      stats,
    },
  });
});

/**
 * 更新用户信息
 */
export const updateProfile = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  if (!req.user) {
    throw new AppError('未登录', 401);
  }

  const { username } = req.body;
  const userId = req.user.userId;

  // 检查用户名是否已被其他用户使用
  if (username) {
    const existingUser = await UserModel.findByUsername(username);
    if (existingUser && existingUser.id !== userId) {
      throw new AppError('用户名已被使用', 409);
    }
  }

  // 更新用户信息
  const updatedUser = await UserModel.update(userId, { username });
  if (!updatedUser) {
    throw new AppError('更新失败', 500);
  }

  res.json({
    success: true,
    message: '更新成功',
    data: {
      user: {
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        points_balance: updatedUser.points_balance,
        email_verified: updatedUser.email_verified,
      },
    },
  });
});

/**
 * 请求密码重置
 */
export const requestPasswordReset = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const { email } = req.body;

  const user = await UserModel.findByEmail(email);
  if (!user) {
    // 为了安全，即使用户不存在也返回成功
    res.json({
      success: true,
      message: '如果邮箱存在，重置链接已发送',
    });
    return;
  }

  // 生成重置令牌
  const resetToken = await UserModel.setPasswordResetToken(email);
  if (!resetToken) {
    throw new AppError('生成重置令牌失败', 500);
  }

  // TODO: 发送重置邮件
  logger.info(`密码重置请求: ${email}, token: ${resetToken}`);

  res.json({
    success: true,
    message: '重置链接已发送到您的邮箱',
  });
});

/**
 * 重置密码
 */
export const resetPassword = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const { token, password } = req.body;

  const user = await UserModel.resetPassword(token, password);
  if (!user) {
    throw new AppError('重置令牌无效或已过期', 400);
  }

  logger.info(`密码重置成功: ${user.email}`);

  res.json({
    success: true,
    message: '密码重置成功',
  });
});

/**
 * 验证邮箱
 */
export const verifyEmail = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  const { token } = req.params;

  const user = await UserModel.verifyEmail(token);
  if (!user) {
    throw new AppError('验证令牌无效', 400);
  }

  logger.info(`邮箱验证成功: ${user.email}`);

  res.json({
    success: true,
    message: '邮箱验证成功',
  });
});

/**
 * 绑定社交账号
 */
export const bindSocialAccount = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  if (!req.user) {
    throw new AppError('未登录', 401);
  }

  const { platform, user_id, username } = req.body;
  const userId = req.user.userId;

  const user = await UserModel.findById(userId);
  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  // 更新社交账号信息
  const socialAccounts = user.social_accounts || {};
  socialAccounts[platform] = {
    user_id,
    username,
    verified: true, // TODO: 实际验证逻辑
  };

  const updatedUser = await UserModel.updateSocialAccounts(userId, socialAccounts);
  if (!updatedUser) {
    throw new AppError('绑定失败', 500);
  }

  logger.info(`社交账号绑定: ${user.username} -> ${platform}:${username}`);

  res.json({
    success: true,
    message: '绑定成功',
    data: {
      social_accounts: updatedUser.social_accounts,
    },
  });
});

/**
 * 解绑社交账号
 */
export const unbindSocialAccount = asyncHandler(async (
  req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  if (!req.user) {
    throw new AppError('未登录', 401);
  }

  const { platform } = req.params;
  const userId = req.user.userId;

  const user = await UserModel.findById(userId);
  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  // 移除社交账号信息
  const socialAccounts = user.social_accounts || {};
  delete socialAccounts[platform];

  const updatedUser = await UserModel.updateSocialAccounts(userId, socialAccounts);
  if (!updatedUser) {
    throw new AppError('解绑失败', 500);
  }

  logger.info(`社交账号解绑: ${user.username} -> ${platform}`);

  res.json({
    success: true,
    message: '解绑成功',
    data: {
      social_accounts: updatedUser.social_accounts,
    },
  });
});
