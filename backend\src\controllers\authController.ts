import { Request, Response } from 'express';
import { asyncHandler } from '@/middleware/errorHandler';
import { ApiResponse, LoginRequest, RegisterRequest } from '@/types';
import { generateAccessToken, generateRefreshToken } from '@/utils/jwt';
import logger from '@/utils/logger';

/**
 * 用户注册
 */
export const register = asyncHandler(async (
  _req: Request<{}, ApiResponse, RegisterRequest>,
  res: Response<ApiResponse>
): Promise<void> => {
  // 暂时返回演示数据，因为数据库未连接
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式，请访问 /api/demo/info 查看演示数据'
  });
});

/**
 * 用户登录
 */
export const login = asyncHandler(async (
  _req: Request<{}, ApiResponse, LoginRequest>,
  res: Response<ApiResponse>
): Promise<void> => {
  // 暂时返回演示数据，因为数据库未连接
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式，请访问 /api/demo/info 查看演示数据'
  });
});

/**
 * 获取当前用户信息
 */
export const getCurrentUser = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式，请访问 /api/demo/user 查看演示数据'
  });
});

/**
 * 更新用户信息
 */
export const updateProfile = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式，请访问 /api/demo/user 查看演示数据'
  });
});

/**
 * 请求密码重置
 */
export const requestPasswordReset = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 重置密码
 */
export const resetPassword = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 验证邮箱
 */
export const verifyEmail = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 绑定社交账号
 */
export const bindSocialAccount = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});

/**
 * 解绑社交账号
 */
export const unbindSocialAccount = asyncHandler(async (
  _req: Request,
  res: Response<ApiResponse>
): Promise<void> => {
  res.status(503).json({
    success: false,
    error: '数据库服务不可用，请稍后再试',
    message: '当前运行在演示模式'
  });
});
