import { db } from '@/config/database';
import { Task, TaskFilters, Platform, TaskType, TaskStatus } from '@/types';

export class TaskModel {
  private static tableName = 'tasks';

  /**
   * 根据ID查找任务
   */
  static async findById(id: number): Promise<Task | null> {
    const task = await db(this.tableName).where({ id }).first();
    return task || null;
  }

  /**
   * 创建新任务
   */
  static async create(taskData: {
    publisher_id: number;
    title: string;
    description?: string;
    video_url: string;
    platform: Platform;
    task_type: TaskType;
    reward_points: number;
    total_quota: number;
    expires_at: Date;
    verification_rules?: any;
  }): Promise<Task> {
    const [task] = await db(this.tableName)
      .insert({
        ...taskData,
        verification_rules: JSON.stringify(taskData.verification_rules || {}),
      })
      .returning('*');

    return task;
  }

  /**
   * 获取任务列表（带分页和筛选）
   */
  static async findMany(
    filters: TaskFilters = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{
    tasks: Task[];
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  }> {
    let query = db(this.tableName)
      .select('tasks.*', 'users.username as publisher_username')
      .leftJoin('users', 'tasks.publisher_id', 'users.id')
      .where('tasks.status', 'active')
      .where('tasks.expires_at', '>', new Date())
      .where('tasks.completed_count', '<', db.raw('tasks.total_quota'));

    // 应用筛选条件
    if (filters.platform) {
      query = query.where('tasks.platform', filters.platform);
    }

    if (filters.task_type) {
      query = query.where('tasks.task_type', filters.task_type);
    }

    if (filters.min_points) {
      query = query.where('tasks.reward_points', '>=', filters.min_points);
    }

    if (filters.max_points) {
      query = query.where('tasks.reward_points', '<=', filters.max_points);
    }

    if (filters.search) {
      query = query.where('tasks.title', 'ilike', `%${filters.search}%`);
    }

    // 获取总数
    const totalQuery = query.clone();
    const [{ count }] = await totalQuery.count('* as count');
    const total = Number(count);

    // 分页
    const offset = (page - 1) * limit;
    const tasks = await query
      .orderBy('tasks.created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      tasks,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    };
  }

  /**
   * 获取用户发布的任务
   */
  static async findByPublisher(
    publisherId: number,
    page: number = 1,
    limit: number = 20
  ): Promise<{
    tasks: Task[];
    total: number;
    totalPages: number;
  }> {
    const offset = (page - 1) * limit;

    const [{ count }] = await db(this.tableName)
      .where({ publisher_id: publisherId })
      .count('* as count');

    const tasks = await db(this.tableName)
      .where({ publisher_id: publisherId })
      .orderBy('created_at', 'desc')
      .limit(limit)
      .offset(offset);

    const total = Number(count);
    const totalPages = Math.ceil(total / limit);

    return {
      tasks,
      total,
      totalPages,
    };
  }

  /**
   * 更新任务状态
   */
  static async updateStatus(id: number, status: TaskStatus): Promise<Task | null> {
    const [task] = await db(this.tableName)
      .where({ id })
      .update({
        status,
        updated_at: new Date(),
      })
      .returning('*');

    return task || null;
  }

  /**
   * 增加任务完成数量
   */
  static async incrementCompletedCount(id: number): Promise<Task | null> {
    const [task] = await db(this.tableName)
      .where({ id })
      .increment('completed_count', 1)
      .update({ updated_at: new Date() })
      .returning('*');

    return task || null;
  }

  /**
   * 减少任务完成数量
   */
  static async decrementCompletedCount(id: number): Promise<Task | null> {
    const [task] = await db(this.tableName)
      .where({ id })
      .where('completed_count', '>', 0)
      .decrement('completed_count', 1)
      .update({ updated_at: new Date() })
      .returning('*');

    return task || null;
  }

  /**
   * 检查任务是否可以执行
   */
  static async canExecute(taskId: number, userId: number): Promise<{
    canExecute: boolean;
    reason?: string;
  }> {
    const task = await this.findById(taskId);
    if (!task) {
      return { canExecute: false, reason: '任务不存在' };
    }

    if (task.status !== 'active') {
      return { canExecute: false, reason: '任务已暂停或完成' };
    }

    if (new Date() > new Date(task.expires_at)) {
      return { canExecute: false, reason: '任务已过期' };
    }

    if (task.completed_count >= task.total_quota) {
      return { canExecute: false, reason: '任务名额已满' };
    }

    if (task.publisher_id === userId) {
      return { canExecute: false, reason: '不能执行自己发布的任务' };
    }

    // 检查是否已经执行过
    const existingExecution = await db('task_executions')
      .where({ task_id: taskId, executor_id: userId })
      .first();

    if (existingExecution) {
      return { canExecute: false, reason: '已经执行过该任务' };
    }

    return { canExecute: true };
  }

  /**
   * 获取任务详情（包含发布者信息）
   */
  static async getTaskDetails(id: number): Promise<Task & { publisher_username: string } | null> {
    const task = await db(this.tableName)
      .select('tasks.*', 'users.username as publisher_username')
      .leftJoin('users', 'tasks.publisher_id', 'users.id')
      .where('tasks.id', id)
      .first();

    return task || null;
  }

  /**
   * 更新任务信息
   */
  static async update(id: number, updateData: Partial<Task>): Promise<Task | null> {
    const [task] = await db(this.tableName)
      .where({ id })
      .update({
        ...updateData,
        updated_at: new Date(),
      })
      .returning('*');

    return task || null;
  }

  /**
   * 删除任务
   */
  static async delete(id: number): Promise<boolean> {
    const deletedCount = await db(this.tableName).where({ id }).del();
    return deletedCount > 0;
  }

  /**
   * 获取平台统计
   */
  static async getPlatformStats(): Promise<Record<Platform, number>> {
    const stats = await db(this.tableName)
      .select('platform')
      .count('* as count')
      .where('status', 'active')
      .groupBy('platform');

    const result: Record<Platform, number> = {
      douyin: 0,
      kuaishou: 0,
      xiaohongshu: 0,
    };

    stats.forEach((stat) => {
      result[stat.platform as Platform] = Number(stat.count);
    });

    return result;
  }

  /**
   * 自动更新过期任务状态
   */
  static async updateExpiredTasks(): Promise<number> {
    const updatedCount = await db(this.tableName)
      .where('status', 'active')
      .where('expires_at', '<', new Date())
      .update({
        status: 'expired',
        updated_at: new Date(),
      });

    return updatedCount;
  }
}
