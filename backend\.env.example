# 服务器配置
NODE_ENV=development
PORT=3000
HOST=localhost

# 数据库配置
DATABASE_URL=postgresql://postgres:postgres123@localhost:5432/mutual_like_platform
DB_HOST=localhost
DB_PORT=5432
DB_NAME=mutual_like_platform
DB_USER=postgres
DB_PASSWORD=postgres123

# Redis配置
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 邮件服务配置
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# 任务配置
MAX_TASKS_PER_USER=10
MAX_TASK_EXECUTIONS_PER_DAY=50
TASK_VERIFICATION_TIMEOUT=300000

# 积分配置
INITIAL_POINTS=100
MIN_TASK_POINTS=1
MAX_TASK_POINTS=50

# 外部API配置
DOUYIN_API_KEY=your-douyin-api-key
KUAISHOU_API_KEY=your-kuaishou-api-key
XIAOHONGSHU_API_KEY=your-xiaohongshu-api-key

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# CORS配置
CORS_ORIGIN=http://localhost:5173
CORS_CREDENTIALS=true
