import{r as T,a as Bn,b as Hn,c as M,g as zn}from"./vendor-BD2DzXP7.js";import{u as Es,L as Me,a as Kn,O as Wn,R as Jn,b as me,N as br,B as Gn}from"./router-Bw_JL_6y.js";import{M as Os,L as sr,E as nr,a as ir,U as Xe,C as jt,T as Zn,A as Xn,b as Yn,c as ei,P as Rs,d as _s,e as ti,S as qr,F as ri,H as Ir,f as si,g as ni,h as ii,i as ai,j as oi,X as li,k as ui,l as ci,m as di,n as fi,B as hi}from"./ui-CUNRZxwD.js";(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))s(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&s(a)}).observe(document,{childList:!0,subtree:!0});function r(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function s(n){if(n.ep)return;n.ep=!0;const i=r(n);fetch(n.href,i)}})();var Cs={exports:{}},kt={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mi=T,pi=Symbol.for("react.element"),yi=Symbol.for("react.fragment"),vi=Object.prototype.hasOwnProperty,gi=mi.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,xi={key:!0,ref:!0,__self:!0,__source:!0};function As(e,t,r){var s,n={},i=null,a=null;r!==void 0&&(i=""+r),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(a=t.ref);for(s in t)vi.call(t,s)&&!xi.hasOwnProperty(s)&&(n[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps,t)n[s]===void 0&&(n[s]=t[s]);return{$$typeof:pi,type:e,key:i,ref:a,props:n,_owner:gi.current}}kt.Fragment=yi;kt.jsx=As;kt.jsxs=As;Cs.exports=kt;var l=Cs.exports,ar={},$r=Bn;ar.createRoot=$r.createRoot,ar.hydrateRoot=$r.hydrateRoot;function or(e,t){return or=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,s){return r.__proto__=s,r},or(e,t)}function Be(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,or(e,t)}var He=function(){function e(){this.listeners=[]}var t=e.prototype;return t.subscribe=function(s){var n=this,i=s||function(){};return this.listeners.push(i),this.onSubscribe(),function(){n.listeners=n.listeners.filter(function(a){return a!==i}),n.onUnsubscribe()}},t.hasListeners=function(){return this.listeners.length>0},t.onSubscribe=function(){},t.onUnsubscribe=function(){},e}();function D(){return D=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)({}).hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e},D.apply(null,arguments)}var St=typeof window>"u";function ee(){}function bi(e,t){return typeof e=="function"?e(t):e}function lr(e){return typeof e=="number"&&e>=0&&e!==1/0}function Nt(e){return Array.isArray(e)?e:[e]}function Fs(e,t){return Math.max(e+(t||0)-Date.now(),0)}function mt(e,t,r){return ot(e)?typeof t=="function"?D({},r,{queryKey:e,queryFn:t}):D({},t,{queryKey:e}):e}function wi(e,t,r){return ot(e)?typeof t=="function"?D({},r,{mutationKey:e,mutationFn:t}):D({},t,{mutationKey:e}):typeof e=="function"?D({},t,{mutationFn:e}):D({},e)}function Ce(e,t,r){return ot(e)?[D({},t,{queryKey:e}),r]:[e||{},t]}function ji(e,t){if(e===!0&&t===!0||e==null&&t==null)return"all";if(e===!1&&t===!1)return"none";var r=e??!t;return r?"active":"inactive"}function Qr(e,t){var r=e.active,s=e.exact,n=e.fetching,i=e.inactive,a=e.predicate,o=e.queryKey,c=e.stale;if(ot(o)){if(s){if(t.queryHash!==wr(o,t.options))return!1}else if(!Et(t.queryKey,o))return!1}var u=ji(r,i);if(u==="none")return!1;if(u!=="all"){var f=t.isActive();if(u==="active"&&!f||u==="inactive"&&f)return!1}return!(typeof c=="boolean"&&t.isStale()!==c||typeof n=="boolean"&&t.isFetching()!==n||a&&!a(t))}function Vr(e,t){var r=e.exact,s=e.fetching,n=e.predicate,i=e.mutationKey;if(ot(i)){if(!t.options.mutationKey)return!1;if(r){if(Te(t.options.mutationKey)!==Te(i))return!1}else if(!Et(t.options.mutationKey,i))return!1}return!(typeof s=="boolean"&&t.state.status==="loading"!==s||n&&!n(t))}function wr(e,t){var r=(t==null?void 0:t.queryKeyHashFn)||Te;return r(e)}function Te(e){var t=Nt(e);return Si(t)}function Si(e){return JSON.stringify(e,function(t,r){return ur(r)?Object.keys(r).sort().reduce(function(s,n){return s[n]=r[n],s},{}):r})}function Et(e,t){return Ps(Nt(e),Nt(t))}function Ps(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(function(r){return!Ps(e[r],t[r])}):!1}function Ot(e,t){if(e===t)return e;var r=Array.isArray(e)&&Array.isArray(t);if(r||ur(e)&&ur(t)){for(var s=r?e.length:Object.keys(e).length,n=r?t:Object.keys(t),i=n.length,a=r?[]:{},o=0,c=0;c<i;c++){var u=r?c:n[c];a[u]=Ot(e[u],t[u]),a[u]===e[u]&&o++}return s===i&&o===s?e:a}return t}function Ni(e,t){if(e&&!t||t&&!e)return!1;for(var r in e)if(e[r]!==t[r])return!1;return!0}function ur(e){if(!Br(e))return!1;var t=e.constructor;if(typeof t>"u")return!0;var r=t.prototype;return!(!Br(r)||!r.hasOwnProperty("isPrototypeOf"))}function Br(e){return Object.prototype.toString.call(e)==="[object Object]"}function ot(e){return typeof e=="string"||Array.isArray(e)}function Ei(e){return new Promise(function(t){setTimeout(t,e)})}function Hr(e){Promise.resolve().then(e).catch(function(t){return setTimeout(function(){throw t})})}function Ts(){if(typeof AbortController=="function")return new AbortController}var Oi=function(e){Be(t,e);function t(){var s;return s=e.call(this)||this,s.setup=function(n){var i;if(!St&&((i=window)!=null&&i.addEventListener)){var a=function(){return n()};return window.addEventListener("visibilitychange",a,!1),window.addEventListener("focus",a,!1),function(){window.removeEventListener("visibilitychange",a),window.removeEventListener("focus",a)}}},s}var r=t.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},r.setEventListener=function(n){var i,a=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(o){typeof o=="boolean"?a.setFocused(o):a.onFocus()})},r.setFocused=function(n){this.focused=n,n&&this.onFocus()},r.onFocus=function(){this.listeners.forEach(function(n){n()})},r.isFocused=function(){return typeof this.focused=="boolean"?this.focused:typeof document>"u"?!0:[void 0,"visible","prerender"].includes(document.visibilityState)},t}(He),Ye=new Oi,Ri=function(e){Be(t,e);function t(){var s;return s=e.call(this)||this,s.setup=function(n){var i;if(!St&&((i=window)!=null&&i.addEventListener)){var a=function(){return n()};return window.addEventListener("online",a,!1),window.addEventListener("offline",a,!1),function(){window.removeEventListener("online",a),window.removeEventListener("offline",a)}}},s}var r=t.prototype;return r.onSubscribe=function(){this.cleanup||this.setEventListener(this.setup)},r.onUnsubscribe=function(){if(!this.hasListeners()){var n;(n=this.cleanup)==null||n.call(this),this.cleanup=void 0}},r.setEventListener=function(n){var i,a=this;this.setup=n,(i=this.cleanup)==null||i.call(this),this.cleanup=n(function(o){typeof o=="boolean"?a.setOnline(o):a.onOnline()})},r.setOnline=function(n){this.online=n,n&&this.onOnline()},r.onOnline=function(){this.listeners.forEach(function(n){n()})},r.isOnline=function(){return typeof this.online=="boolean"?this.online:typeof navigator>"u"||typeof navigator.onLine>"u"?!0:navigator.onLine},t}(He),pt=new Ri;function _i(e){return Math.min(1e3*Math.pow(2,e),3e4)}function Rt(e){return typeof(e==null?void 0:e.cancel)=="function"}var Ds=function(t){this.revert=t==null?void 0:t.revert,this.silent=t==null?void 0:t.silent};function yt(e){return e instanceof Ds}var ks=function(t){var r=this,s=!1,n,i,a,o;this.abort=t.abort,this.cancel=function(v){return n==null?void 0:n(v)},this.cancelRetry=function(){s=!0},this.continueRetry=function(){s=!1},this.continue=function(){return i==null?void 0:i()},this.failureCount=0,this.isPaused=!1,this.isResolved=!1,this.isTransportCancelable=!1,this.promise=new Promise(function(v,b){a=v,o=b});var c=function(b){r.isResolved||(r.isResolved=!0,t.onSuccess==null||t.onSuccess(b),i==null||i(),a(b))},u=function(b){r.isResolved||(r.isResolved=!0,t.onError==null||t.onError(b),i==null||i(),o(b))},f=function(){return new Promise(function(b){i=b,r.isPaused=!0,t.onPause==null||t.onPause()}).then(function(){i=void 0,r.isPaused=!1,t.onContinue==null||t.onContinue()})},m=function v(){if(!r.isResolved){var b;try{b=t.fn()}catch(g){b=Promise.reject(g)}n=function(h){if(!r.isResolved&&(u(new Ds(h)),r.abort==null||r.abort(),Rt(b)))try{b.cancel()}catch{}},r.isTransportCancelable=Rt(b),Promise.resolve(b).then(c).catch(function(g){var h,w;if(!r.isResolved){var O=(h=t.retry)!=null?h:3,j=(w=t.retryDelay)!=null?w:_i,N=typeof j=="function"?j(r.failureCount,g):j,k=O===!0||typeof O=="number"&&r.failureCount<O||typeof O=="function"&&O(r.failureCount,g);if(s||!k){u(g);return}r.failureCount++,t.onFail==null||t.onFail(r.failureCount,g),Ei(N).then(function(){if(!Ye.isFocused()||!pt.isOnline())return f()}).then(function(){s?u(g):v()})}})}};m()},Ci=function(){function e(){this.queue=[],this.transactions=0,this.notifyFn=function(r){r()},this.batchNotifyFn=function(r){r()}}var t=e.prototype;return t.batch=function(s){var n;this.transactions++;try{n=s()}finally{this.transactions--,this.transactions||this.flush()}return n},t.schedule=function(s){var n=this;this.transactions?this.queue.push(s):Hr(function(){n.notifyFn(s)})},t.batchCalls=function(s){var n=this;return function(){for(var i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];n.schedule(function(){s.apply(void 0,a)})}},t.flush=function(){var s=this,n=this.queue;this.queue=[],n.length&&Hr(function(){s.batchNotifyFn(function(){n.forEach(function(i){s.notifyFn(i)})})})},t.setNotifyFunction=function(s){this.notifyFn=s},t.setBatchNotifyFunction=function(s){this.batchNotifyFn=s},e}(),B=new Ci,Ls=console;function _t(){return Ls}function Ai(e){Ls=e}var Fi=function(){function e(r){this.abortSignalConsumed=!1,this.hadObservers=!1,this.defaultOptions=r.defaultOptions,this.setOptions(r.options),this.observers=[],this.cache=r.cache,this.queryKey=r.queryKey,this.queryHash=r.queryHash,this.initialState=r.state||this.getDefaultState(this.options),this.state=this.initialState,this.meta=r.meta,this.scheduleGc()}var t=e.prototype;return t.setOptions=function(s){var n;this.options=D({},this.defaultOptions,s),this.meta=s==null?void 0:s.meta,this.cacheTime=Math.max(this.cacheTime||0,(n=this.options.cacheTime)!=null?n:5*60*1e3)},t.setDefaultOptions=function(s){this.defaultOptions=s},t.scheduleGc=function(){var s=this;this.clearGcTimeout(),lr(this.cacheTime)&&(this.gcTimeout=setTimeout(function(){s.optionalRemove()},this.cacheTime))},t.clearGcTimeout=function(){this.gcTimeout&&(clearTimeout(this.gcTimeout),this.gcTimeout=void 0)},t.optionalRemove=function(){this.observers.length||(this.state.isFetching?this.hadObservers&&this.scheduleGc():this.cache.remove(this))},t.setData=function(s,n){var i,a,o=this.state.data,c=bi(s,o);return(i=(a=this.options).isDataEqual)!=null&&i.call(a,o,c)?c=o:this.options.structuralSharing!==!1&&(c=Ot(o,c)),this.dispatch({data:c,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt}),c},t.setState=function(s,n){this.dispatch({type:"setState",state:s,setStateOptions:n})},t.cancel=function(s){var n,i=this.promise;return(n=this.retryer)==null||n.cancel(s),i?i.then(ee).catch(ee):Promise.resolve()},t.destroy=function(){this.clearGcTimeout(),this.cancel({silent:!0})},t.reset=function(){this.destroy(),this.setState(this.initialState)},t.isActive=function(){return this.observers.some(function(s){return s.options.enabled!==!1})},t.isFetching=function(){return this.state.isFetching},t.isStale=function(){return this.state.isInvalidated||!this.state.dataUpdatedAt||this.observers.some(function(s){return s.getCurrentResult().isStale})},t.isStaleByTime=function(s){return s===void 0&&(s=0),this.state.isInvalidated||!this.state.dataUpdatedAt||!Fs(this.state.dataUpdatedAt,s)},t.onFocus=function(){var s,n=this.observers.find(function(i){return i.shouldFetchOnWindowFocus()});n&&n.refetch(),(s=this.retryer)==null||s.continue()},t.onOnline=function(){var s,n=this.observers.find(function(i){return i.shouldFetchOnReconnect()});n&&n.refetch(),(s=this.retryer)==null||s.continue()},t.addObserver=function(s){this.observers.indexOf(s)===-1&&(this.observers.push(s),this.hadObservers=!0,this.clearGcTimeout(),this.cache.notify({type:"observerAdded",query:this,observer:s}))},t.removeObserver=function(s){this.observers.indexOf(s)!==-1&&(this.observers=this.observers.filter(function(n){return n!==s}),this.observers.length||(this.retryer&&(this.retryer.isTransportCancelable||this.abortSignalConsumed?this.retryer.cancel({revert:!0}):this.retryer.cancelRetry()),this.cacheTime?this.scheduleGc():this.cache.remove(this)),this.cache.notify({type:"observerRemoved",query:this,observer:s}))},t.getObserversCount=function(){return this.observers.length},t.invalidate=function(){this.state.isInvalidated||this.dispatch({type:"invalidate"})},t.fetch=function(s,n){var i=this,a,o,c;if(this.state.isFetching){if(this.state.dataUpdatedAt&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(this.promise){var u;return(u=this.retryer)==null||u.continueRetry(),this.promise}}if(s&&this.setOptions(s),!this.options.queryFn){var f=this.observers.find(function(j){return j.options.queryFn});f&&this.setOptions(f.options)}var m=Nt(this.queryKey),v=Ts(),b={queryKey:m,pageParam:void 0,meta:this.meta};Object.defineProperty(b,"signal",{enumerable:!0,get:function(){if(v)return i.abortSignalConsumed=!0,v.signal}});var g=function(){return i.options.queryFn?(i.abortSignalConsumed=!1,i.options.queryFn(b)):Promise.reject("Missing queryFn")},h={fetchOptions:n,options:this.options,queryKey:m,state:this.state,fetchFn:g,meta:this.meta};if((a=this.options.behavior)!=null&&a.onFetch){var w;(w=this.options.behavior)==null||w.onFetch(h)}if(this.revertState=this.state,!this.state.isFetching||this.state.fetchMeta!==((o=h.fetchOptions)==null?void 0:o.meta)){var O;this.dispatch({type:"fetch",meta:(O=h.fetchOptions)==null?void 0:O.meta})}return this.retryer=new ks({fn:h.fetchFn,abort:v==null||(c=v.abort)==null?void 0:c.bind(v),onSuccess:function(N){i.setData(N),i.cache.config.onSuccess==null||i.cache.config.onSuccess(N,i),i.cacheTime===0&&i.optionalRemove()},onError:function(N){yt(N)&&N.silent||i.dispatch({type:"error",error:N}),yt(N)||(i.cache.config.onError==null||i.cache.config.onError(N,i),_t().error(N)),i.cacheTime===0&&i.optionalRemove()},onFail:function(){i.dispatch({type:"failed"})},onPause:function(){i.dispatch({type:"pause"})},onContinue:function(){i.dispatch({type:"continue"})},retry:h.options.retry,retryDelay:h.options.retryDelay}),this.promise=this.retryer.promise,this.promise},t.dispatch=function(s){var n=this;this.state=this.reducer(this.state,s),B.batch(function(){n.observers.forEach(function(i){i.onQueryUpdate(s)}),n.cache.notify({query:n,type:"queryUpdated",action:s})})},t.getDefaultState=function(s){var n=typeof s.initialData=="function"?s.initialData():s.initialData,i=typeof s.initialData<"u",a=i?typeof s.initialDataUpdatedAt=="function"?s.initialDataUpdatedAt():s.initialDataUpdatedAt:0,o=typeof n<"u";return{data:n,dataUpdateCount:0,dataUpdatedAt:o?a??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchMeta:null,isFetching:!1,isInvalidated:!1,isPaused:!1,status:o?"success":"idle"}},t.reducer=function(s,n){var i,a;switch(n.type){case"failed":return D({},s,{fetchFailureCount:s.fetchFailureCount+1});case"pause":return D({},s,{isPaused:!0});case"continue":return D({},s,{isPaused:!1});case"fetch":return D({},s,{fetchFailureCount:0,fetchMeta:(i=n.meta)!=null?i:null,isFetching:!0,isPaused:!1},!s.dataUpdatedAt&&{error:null,status:"loading"});case"success":return D({},s,{data:n.data,dataUpdateCount:s.dataUpdateCount+1,dataUpdatedAt:(a=n.dataUpdatedAt)!=null?a:Date.now(),error:null,fetchFailureCount:0,isFetching:!1,isInvalidated:!1,isPaused:!1,status:"success"});case"error":var o=n.error;return yt(o)&&o.revert&&this.revertState?D({},this.revertState):D({},s,{error:o,errorUpdateCount:s.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:s.fetchFailureCount+1,isFetching:!1,isPaused:!1,status:"error"});case"invalidate":return D({},s,{isInvalidated:!0});case"setState":return D({},s,n.state);default:return s}},e}(),Pi=function(e){Be(t,e);function t(s){var n;return n=e.call(this)||this,n.config=s||{},n.queries=[],n.queriesMap={},n}var r=t.prototype;return r.build=function(n,i,a){var o,c=i.queryKey,u=(o=i.queryHash)!=null?o:wr(c,i),f=this.get(u);return f||(f=new Fi({cache:this,queryKey:c,queryHash:u,options:n.defaultQueryOptions(i),state:a,defaultOptions:n.getQueryDefaults(c),meta:i.meta}),this.add(f)),f},r.add=function(n){this.queriesMap[n.queryHash]||(this.queriesMap[n.queryHash]=n,this.queries.push(n),this.notify({type:"queryAdded",query:n}))},r.remove=function(n){var i=this.queriesMap[n.queryHash];i&&(n.destroy(),this.queries=this.queries.filter(function(a){return a!==n}),i===n&&delete this.queriesMap[n.queryHash],this.notify({type:"queryRemoved",query:n}))},r.clear=function(){var n=this;B.batch(function(){n.queries.forEach(function(i){n.remove(i)})})},r.get=function(n){return this.queriesMap[n]},r.getAll=function(){return this.queries},r.find=function(n,i){var a=Ce(n,i),o=a[0];return typeof o.exact>"u"&&(o.exact=!0),this.queries.find(function(c){return Qr(o,c)})},r.findAll=function(n,i){var a=Ce(n,i),o=a[0];return Object.keys(o).length>0?this.queries.filter(function(c){return Qr(o,c)}):this.queries},r.notify=function(n){var i=this;B.batch(function(){i.listeners.forEach(function(a){a(n)})})},r.onFocus=function(){var n=this;B.batch(function(){n.queries.forEach(function(i){i.onFocus()})})},r.onOnline=function(){var n=this;B.batch(function(){n.queries.forEach(function(i){i.onOnline()})})},t}(He),Ti=function(){function e(r){this.options=D({},r.defaultOptions,r.options),this.mutationId=r.mutationId,this.mutationCache=r.mutationCache,this.observers=[],this.state=r.state||Us(),this.meta=r.meta}var t=e.prototype;return t.setState=function(s){this.dispatch({type:"setState",state:s})},t.addObserver=function(s){this.observers.indexOf(s)===-1&&this.observers.push(s)},t.removeObserver=function(s){this.observers=this.observers.filter(function(n){return n!==s})},t.cancel=function(){return this.retryer?(this.retryer.cancel(),this.retryer.promise.then(ee).catch(ee)):Promise.resolve()},t.continue=function(){return this.retryer?(this.retryer.continue(),this.retryer.promise):this.execute()},t.execute=function(){var s=this,n,i=this.state.status==="loading",a=Promise.resolve();return i||(this.dispatch({type:"loading",variables:this.options.variables}),a=a.then(function(){s.mutationCache.config.onMutate==null||s.mutationCache.config.onMutate(s.state.variables,s)}).then(function(){return s.options.onMutate==null?void 0:s.options.onMutate(s.state.variables)}).then(function(o){o!==s.state.context&&s.dispatch({type:"loading",context:o,variables:s.state.variables})})),a.then(function(){return s.executeMutation()}).then(function(o){n=o,s.mutationCache.config.onSuccess==null||s.mutationCache.config.onSuccess(n,s.state.variables,s.state.context,s)}).then(function(){return s.options.onSuccess==null?void 0:s.options.onSuccess(n,s.state.variables,s.state.context)}).then(function(){return s.options.onSettled==null?void 0:s.options.onSettled(n,null,s.state.variables,s.state.context)}).then(function(){return s.dispatch({type:"success",data:n}),n}).catch(function(o){return s.mutationCache.config.onError==null||s.mutationCache.config.onError(o,s.state.variables,s.state.context,s),_t().error(o),Promise.resolve().then(function(){return s.options.onError==null?void 0:s.options.onError(o,s.state.variables,s.state.context)}).then(function(){return s.options.onSettled==null?void 0:s.options.onSettled(void 0,o,s.state.variables,s.state.context)}).then(function(){throw s.dispatch({type:"error",error:o}),o})})},t.executeMutation=function(){var s=this,n;return this.retryer=new ks({fn:function(){return s.options.mutationFn?s.options.mutationFn(s.state.variables):Promise.reject("No mutationFn found")},onFail:function(){s.dispatch({type:"failed"})},onPause:function(){s.dispatch({type:"pause"})},onContinue:function(){s.dispatch({type:"continue"})},retry:(n=this.options.retry)!=null?n:0,retryDelay:this.options.retryDelay}),this.retryer.promise},t.dispatch=function(s){var n=this;this.state=Di(this.state,s),B.batch(function(){n.observers.forEach(function(i){i.onMutationUpdate(s)}),n.mutationCache.notify(n)})},e}();function Us(){return{context:void 0,data:void 0,error:null,failureCount:0,isPaused:!1,status:"idle",variables:void 0}}function Di(e,t){switch(t.type){case"failed":return D({},e,{failureCount:e.failureCount+1});case"pause":return D({},e,{isPaused:!0});case"continue":return D({},e,{isPaused:!1});case"loading":return D({},e,{context:t.context,data:void 0,error:null,isPaused:!1,status:"loading",variables:t.variables});case"success":return D({},e,{data:t.data,error:null,status:"success",isPaused:!1});case"error":return D({},e,{data:void 0,error:t.error,failureCount:e.failureCount+1,isPaused:!1,status:"error"});case"setState":return D({},e,t.state);default:return e}}var ki=function(e){Be(t,e);function t(s){var n;return n=e.call(this)||this,n.config=s||{},n.mutations=[],n.mutationId=0,n}var r=t.prototype;return r.build=function(n,i,a){var o=new Ti({mutationCache:this,mutationId:++this.mutationId,options:n.defaultMutationOptions(i),state:a,defaultOptions:i.mutationKey?n.getMutationDefaults(i.mutationKey):void 0,meta:i.meta});return this.add(o),o},r.add=function(n){this.mutations.push(n),this.notify(n)},r.remove=function(n){this.mutations=this.mutations.filter(function(i){return i!==n}),n.cancel(),this.notify(n)},r.clear=function(){var n=this;B.batch(function(){n.mutations.forEach(function(i){n.remove(i)})})},r.getAll=function(){return this.mutations},r.find=function(n){return typeof n.exact>"u"&&(n.exact=!0),this.mutations.find(function(i){return Vr(n,i)})},r.findAll=function(n){return this.mutations.filter(function(i){return Vr(n,i)})},r.notify=function(n){var i=this;B.batch(function(){i.listeners.forEach(function(a){a(n)})})},r.onFocus=function(){this.resumePausedMutations()},r.onOnline=function(){this.resumePausedMutations()},r.resumePausedMutations=function(){var n=this.mutations.filter(function(i){return i.state.isPaused});return B.batch(function(){return n.reduce(function(i,a){return i.then(function(){return a.continue().catch(ee)})},Promise.resolve())})},t}(He);function Li(){return{onFetch:function(t){t.fetchFn=function(){var r,s,n,i,a,o,c=(r=t.fetchOptions)==null||(s=r.meta)==null?void 0:s.refetchPage,u=(n=t.fetchOptions)==null||(i=n.meta)==null?void 0:i.fetchMore,f=u==null?void 0:u.pageParam,m=(u==null?void 0:u.direction)==="forward",v=(u==null?void 0:u.direction)==="backward",b=((a=t.state.data)==null?void 0:a.pages)||[],g=((o=t.state.data)==null?void 0:o.pageParams)||[],h=Ts(),w=h==null?void 0:h.signal,O=g,j=!1,N=t.options.queryFn||function(){return Promise.reject("Missing queryFn")},k=function(q,z,Q,te){return O=te?[z].concat(O):[].concat(O,[z]),te?[Q].concat(q):[].concat(q,[Q])},A=function(q,z,Q,te){if(j)return Promise.reject("Cancelled");if(typeof Q>"u"&&!z&&q.length)return Promise.resolve(q);var oe={queryKey:t.queryKey,signal:w,pageParam:Q,meta:t.meta},le=N(oe),je=Promise.resolve(le).then(function(zt){return k(q,Q,zt,te)});if(Rt(le)){var $e=je;$e.cancel=le.cancel}return je},U;if(!b.length)U=A([]);else if(m){var H=typeof f<"u",fe=H?f:zr(t.options,b);U=A(b,H,fe)}else if(v){var G=typeof f<"u",Z=G?f:Ui(t.options,b);U=A(b,G,Z,!0)}else(function(){O=[];var L=typeof t.options.getNextPageParam>"u",q=c&&b[0]?c(b[0],0,b):!0;U=q?A([],L,g[0]):Promise.resolve(k([],g[0],b[0]));for(var z=function(oe){U=U.then(function(le){var je=c&&b[oe]?c(b[oe],oe,b):!0;if(je){var $e=L?g[oe]:zr(t.options,le);return A(le,L,$e)}return Promise.resolve(k(le,g[oe],b[oe]))})},Q=1;Q<b.length;Q++)z(Q)})();var ne=U.then(function(L){return{pages:L,pageParams:O}}),P=ne;return P.cancel=function(){j=!0,h==null||h.abort(),Rt(U)&&U.cancel()},ne}}}}function zr(e,t){return e.getNextPageParam==null?void 0:e.getNextPageParam(t[t.length-1],t)}function Ui(e,t){return e.getPreviousPageParam==null?void 0:e.getPreviousPageParam(t[0],t)}var Mi=function(){function e(r){r===void 0&&(r={}),this.queryCache=r.queryCache||new Pi,this.mutationCache=r.mutationCache||new ki,this.defaultOptions=r.defaultOptions||{},this.queryDefaults=[],this.mutationDefaults=[]}var t=e.prototype;return t.mount=function(){var s=this;this.unsubscribeFocus=Ye.subscribe(function(){Ye.isFocused()&&pt.isOnline()&&(s.mutationCache.onFocus(),s.queryCache.onFocus())}),this.unsubscribeOnline=pt.subscribe(function(){Ye.isFocused()&&pt.isOnline()&&(s.mutationCache.onOnline(),s.queryCache.onOnline())})},t.unmount=function(){var s,n;(s=this.unsubscribeFocus)==null||s.call(this),(n=this.unsubscribeOnline)==null||n.call(this)},t.isFetching=function(s,n){var i=Ce(s,n),a=i[0];return a.fetching=!0,this.queryCache.findAll(a).length},t.isMutating=function(s){return this.mutationCache.findAll(D({},s,{fetching:!0})).length},t.getQueryData=function(s,n){var i;return(i=this.queryCache.find(s,n))==null?void 0:i.state.data},t.getQueriesData=function(s){return this.getQueryCache().findAll(s).map(function(n){var i=n.queryKey,a=n.state,o=a.data;return[i,o]})},t.setQueryData=function(s,n,i){var a=mt(s),o=this.defaultQueryOptions(a);return this.queryCache.build(this,o).setData(n,i)},t.setQueriesData=function(s,n,i){var a=this;return B.batch(function(){return a.getQueryCache().findAll(s).map(function(o){var c=o.queryKey;return[c,a.setQueryData(c,n,i)]})})},t.getQueryState=function(s,n){var i;return(i=this.queryCache.find(s,n))==null?void 0:i.state},t.removeQueries=function(s,n){var i=Ce(s,n),a=i[0],o=this.queryCache;B.batch(function(){o.findAll(a).forEach(function(c){o.remove(c)})})},t.resetQueries=function(s,n,i){var a=this,o=Ce(s,n,i),c=o[0],u=o[1],f=this.queryCache,m=D({},c,{active:!0});return B.batch(function(){return f.findAll(c).forEach(function(v){v.reset()}),a.refetchQueries(m,u)})},t.cancelQueries=function(s,n,i){var a=this,o=Ce(s,n,i),c=o[0],u=o[1],f=u===void 0?{}:u;typeof f.revert>"u"&&(f.revert=!0);var m=B.batch(function(){return a.queryCache.findAll(c).map(function(v){return v.cancel(f)})});return Promise.all(m).then(ee).catch(ee)},t.invalidateQueries=function(s,n,i){var a,o,c,u=this,f=Ce(s,n,i),m=f[0],v=f[1],b=D({},m,{active:(a=(o=m.refetchActive)!=null?o:m.active)!=null?a:!0,inactive:(c=m.refetchInactive)!=null?c:!1});return B.batch(function(){return u.queryCache.findAll(m).forEach(function(g){g.invalidate()}),u.refetchQueries(b,v)})},t.refetchQueries=function(s,n,i){var a=this,o=Ce(s,n,i),c=o[0],u=o[1],f=B.batch(function(){return a.queryCache.findAll(c).map(function(v){return v.fetch(void 0,D({},u,{meta:{refetchPage:c==null?void 0:c.refetchPage}}))})}),m=Promise.all(f).then(ee);return u!=null&&u.throwOnError||(m=m.catch(ee)),m},t.fetchQuery=function(s,n,i){var a=mt(s,n,i),o=this.defaultQueryOptions(a);typeof o.retry>"u"&&(o.retry=!1);var c=this.queryCache.build(this,o);return c.isStaleByTime(o.staleTime)?c.fetch(o):Promise.resolve(c.state.data)},t.prefetchQuery=function(s,n,i){return this.fetchQuery(s,n,i).then(ee).catch(ee)},t.fetchInfiniteQuery=function(s,n,i){var a=mt(s,n,i);return a.behavior=Li(),this.fetchQuery(a)},t.prefetchInfiniteQuery=function(s,n,i){return this.fetchInfiniteQuery(s,n,i).then(ee).catch(ee)},t.cancelMutations=function(){var s=this,n=B.batch(function(){return s.mutationCache.getAll().map(function(i){return i.cancel()})});return Promise.all(n).then(ee).catch(ee)},t.resumePausedMutations=function(){return this.getMutationCache().resumePausedMutations()},t.executeMutation=function(s){return this.mutationCache.build(this,s).execute()},t.getQueryCache=function(){return this.queryCache},t.getMutationCache=function(){return this.mutationCache},t.getDefaultOptions=function(){return this.defaultOptions},t.setDefaultOptions=function(s){this.defaultOptions=s},t.setQueryDefaults=function(s,n){var i=this.queryDefaults.find(function(a){return Te(s)===Te(a.queryKey)});i?i.defaultOptions=n:this.queryDefaults.push({queryKey:s,defaultOptions:n})},t.getQueryDefaults=function(s){var n;return s?(n=this.queryDefaults.find(function(i){return Et(s,i.queryKey)}))==null?void 0:n.defaultOptions:void 0},t.setMutationDefaults=function(s,n){var i=this.mutationDefaults.find(function(a){return Te(s)===Te(a.mutationKey)});i?i.defaultOptions=n:this.mutationDefaults.push({mutationKey:s,defaultOptions:n})},t.getMutationDefaults=function(s){var n;return s?(n=this.mutationDefaults.find(function(i){return Et(s,i.mutationKey)}))==null?void 0:n.defaultOptions:void 0},t.defaultQueryOptions=function(s){if(s!=null&&s._defaulted)return s;var n=D({},this.defaultOptions.queries,this.getQueryDefaults(s==null?void 0:s.queryKey),s,{_defaulted:!0});return!n.queryHash&&n.queryKey&&(n.queryHash=wr(n.queryKey,n)),n},t.defaultQueryObserverOptions=function(s){return this.defaultQueryOptions(s)},t.defaultMutationOptions=function(s){return s!=null&&s._defaulted?s:D({},this.defaultOptions.mutations,this.getMutationDefaults(s==null?void 0:s.mutationKey),s,{_defaulted:!0})},t.clear=function(){this.queryCache.clear(),this.mutationCache.clear()},e}(),qi=function(e){Be(t,e);function t(s,n){var i;return i=e.call(this)||this,i.client=s,i.options=n,i.trackedProps=[],i.selectError=null,i.bindMethods(),i.setOptions(n),i}var r=t.prototype;return r.bindMethods=function(){this.remove=this.remove.bind(this),this.refetch=this.refetch.bind(this)},r.onSubscribe=function(){this.listeners.length===1&&(this.currentQuery.addObserver(this),Kr(this.currentQuery,this.options)&&this.executeFetch(),this.updateTimers())},r.onUnsubscribe=function(){this.listeners.length||this.destroy()},r.shouldFetchOnReconnect=function(){return cr(this.currentQuery,this.options,this.options.refetchOnReconnect)},r.shouldFetchOnWindowFocus=function(){return cr(this.currentQuery,this.options,this.options.refetchOnWindowFocus)},r.destroy=function(){this.listeners=[],this.clearTimers(),this.currentQuery.removeObserver(this)},r.setOptions=function(n,i){var a=this.options,o=this.currentQuery;if(this.options=this.client.defaultQueryObserverOptions(n),typeof this.options.enabled<"u"&&typeof this.options.enabled!="boolean")throw new Error("Expected enabled to be a boolean");this.options.queryKey||(this.options.queryKey=a.queryKey),this.updateQuery();var c=this.hasListeners();c&&Wr(this.currentQuery,o,this.options,a)&&this.executeFetch(),this.updateResult(i),c&&(this.currentQuery!==o||this.options.enabled!==a.enabled||this.options.staleTime!==a.staleTime)&&this.updateStaleTimeout();var u=this.computeRefetchInterval();c&&(this.currentQuery!==o||this.options.enabled!==a.enabled||u!==this.currentRefetchInterval)&&this.updateRefetchInterval(u)},r.getOptimisticResult=function(n){var i=this.client.defaultQueryObserverOptions(n),a=this.client.getQueryCache().build(this.client,i);return this.createResult(a,i)},r.getCurrentResult=function(){return this.currentResult},r.trackResult=function(n,i){var a=this,o={},c=function(f){a.trackedProps.includes(f)||a.trackedProps.push(f)};return Object.keys(n).forEach(function(u){Object.defineProperty(o,u,{configurable:!1,enumerable:!0,get:function(){return c(u),n[u]}})}),(i.useErrorBoundary||i.suspense)&&c("error"),o},r.getNextResult=function(n){var i=this;return new Promise(function(a,o){var c=i.subscribe(function(u){u.isFetching||(c(),u.isError&&(n!=null&&n.throwOnError)?o(u.error):a(u))})})},r.getCurrentQuery=function(){return this.currentQuery},r.remove=function(){this.client.getQueryCache().remove(this.currentQuery)},r.refetch=function(n){return this.fetch(D({},n,{meta:{refetchPage:n==null?void 0:n.refetchPage}}))},r.fetchOptimistic=function(n){var i=this,a=this.client.defaultQueryObserverOptions(n),o=this.client.getQueryCache().build(this.client,a);return o.fetch().then(function(){return i.createResult(o,a)})},r.fetch=function(n){var i=this;return this.executeFetch(n).then(function(){return i.updateResult(),i.currentResult})},r.executeFetch=function(n){this.updateQuery();var i=this.currentQuery.fetch(this.options,n);return n!=null&&n.throwOnError||(i=i.catch(ee)),i},r.updateStaleTimeout=function(){var n=this;if(this.clearStaleTimeout(),!(St||this.currentResult.isStale||!lr(this.options.staleTime))){var i=Fs(this.currentResult.dataUpdatedAt,this.options.staleTime),a=i+1;this.staleTimeoutId=setTimeout(function(){n.currentResult.isStale||n.updateResult()},a)}},r.computeRefetchInterval=function(){var n;return typeof this.options.refetchInterval=="function"?this.options.refetchInterval(this.currentResult.data,this.currentQuery):(n=this.options.refetchInterval)!=null?n:!1},r.updateRefetchInterval=function(n){var i=this;this.clearRefetchInterval(),this.currentRefetchInterval=n,!(St||this.options.enabled===!1||!lr(this.currentRefetchInterval)||this.currentRefetchInterval===0)&&(this.refetchIntervalId=setInterval(function(){(i.options.refetchIntervalInBackground||Ye.isFocused())&&i.executeFetch()},this.currentRefetchInterval))},r.updateTimers=function(){this.updateStaleTimeout(),this.updateRefetchInterval(this.computeRefetchInterval())},r.clearTimers=function(){this.clearStaleTimeout(),this.clearRefetchInterval()},r.clearStaleTimeout=function(){this.staleTimeoutId&&(clearTimeout(this.staleTimeoutId),this.staleTimeoutId=void 0)},r.clearRefetchInterval=function(){this.refetchIntervalId&&(clearInterval(this.refetchIntervalId),this.refetchIntervalId=void 0)},r.createResult=function(n,i){var a=this.currentQuery,o=this.options,c=this.currentResult,u=this.currentResultState,f=this.currentResultOptions,m=n!==a,v=m?n.state:this.currentQueryInitialState,b=m?this.currentResult:this.previousQueryResult,g=n.state,h=g.dataUpdatedAt,w=g.error,O=g.errorUpdatedAt,j=g.isFetching,N=g.status,k=!1,A=!1,U;if(i.optimisticResults){var H=this.hasListeners(),fe=!H&&Kr(n,i),G=H&&Wr(n,a,i,o);(fe||G)&&(j=!0,h||(N="loading"))}if(i.keepPreviousData&&!g.dataUpdateCount&&(b!=null&&b.isSuccess)&&N!=="error")U=b.data,h=b.dataUpdatedAt,N=b.status,k=!0;else if(i.select&&typeof g.data<"u")if(c&&g.data===(u==null?void 0:u.data)&&i.select===this.selectFn)U=this.selectResult;else try{this.selectFn=i.select,U=i.select(g.data),i.structuralSharing!==!1&&(U=Ot(c==null?void 0:c.data,U)),this.selectResult=U,this.selectError=null}catch(P){_t().error(P),this.selectError=P}else U=g.data;if(typeof i.placeholderData<"u"&&typeof U>"u"&&(N==="loading"||N==="idle")){var Z;if(c!=null&&c.isPlaceholderData&&i.placeholderData===(f==null?void 0:f.placeholderData))Z=c.data;else if(Z=typeof i.placeholderData=="function"?i.placeholderData():i.placeholderData,i.select&&typeof Z<"u")try{Z=i.select(Z),i.structuralSharing!==!1&&(Z=Ot(c==null?void 0:c.data,Z)),this.selectError=null}catch(P){_t().error(P),this.selectError=P}typeof Z<"u"&&(N="success",U=Z,A=!0)}this.selectError&&(w=this.selectError,U=this.selectResult,O=Date.now(),N="error");var ne={status:N,isLoading:N==="loading",isSuccess:N==="success",isError:N==="error",isIdle:N==="idle",data:U,dataUpdatedAt:h,error:w,errorUpdatedAt:O,failureCount:g.fetchFailureCount,errorUpdateCount:g.errorUpdateCount,isFetched:g.dataUpdateCount>0||g.errorUpdateCount>0,isFetchedAfterMount:g.dataUpdateCount>v.dataUpdateCount||g.errorUpdateCount>v.errorUpdateCount,isFetching:j,isRefetching:j&&N!=="loading",isLoadingError:N==="error"&&g.dataUpdatedAt===0,isPlaceholderData:A,isPreviousData:k,isRefetchError:N==="error"&&g.dataUpdatedAt!==0,isStale:jr(n,i),refetch:this.refetch,remove:this.remove};return ne},r.shouldNotifyListeners=function(n,i){if(!i)return!0;var a=this.options,o=a.notifyOnChangeProps,c=a.notifyOnChangePropsExclusions;if(!o&&!c||o==="tracked"&&!this.trackedProps.length)return!0;var u=o==="tracked"?this.trackedProps:o;return Object.keys(n).some(function(f){var m=f,v=n[m]!==i[m],b=u==null?void 0:u.some(function(h){return h===f}),g=c==null?void 0:c.some(function(h){return h===f});return v&&!g&&(!u||b)})},r.updateResult=function(n){var i=this.currentResult;if(this.currentResult=this.createResult(this.currentQuery,this.options),this.currentResultState=this.currentQuery.state,this.currentResultOptions=this.options,!Ni(this.currentResult,i)){var a={cache:!0};(n==null?void 0:n.listeners)!==!1&&this.shouldNotifyListeners(this.currentResult,i)&&(a.listeners=!0),this.notify(D({},a,n))}},r.updateQuery=function(){var n=this.client.getQueryCache().build(this.client,this.options);if(n!==this.currentQuery){var i=this.currentQuery;this.currentQuery=n,this.currentQueryInitialState=n.state,this.previousQueryResult=this.currentResult,this.hasListeners()&&(i==null||i.removeObserver(this),n.addObserver(this))}},r.onQueryUpdate=function(n){var i={};n.type==="success"?i.onSuccess=!0:n.type==="error"&&!yt(n.error)&&(i.onError=!0),this.updateResult(i),this.hasListeners()&&this.updateTimers()},r.notify=function(n){var i=this;B.batch(function(){n.onSuccess?(i.options.onSuccess==null||i.options.onSuccess(i.currentResult.data),i.options.onSettled==null||i.options.onSettled(i.currentResult.data,null)):n.onError&&(i.options.onError==null||i.options.onError(i.currentResult.error),i.options.onSettled==null||i.options.onSettled(void 0,i.currentResult.error)),n.listeners&&i.listeners.forEach(function(a){a(i.currentResult)}),n.cache&&i.client.getQueryCache().notify({query:i.currentQuery,type:"observerResultsUpdated"})})},t}(He);function Ii(e,t){return t.enabled!==!1&&!e.state.dataUpdatedAt&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Kr(e,t){return Ii(e,t)||e.state.dataUpdatedAt>0&&cr(e,t,t.refetchOnMount)}function cr(e,t,r){if(t.enabled!==!1){var s=typeof r=="function"?r(e):r;return s==="always"||s!==!1&&jr(e,t)}return!1}function Wr(e,t,r,s){return r.enabled!==!1&&(e!==t||s.enabled===!1)&&(!r.suspense||e.state.status!=="error")&&jr(e,r)}function jr(e,t){return e.isStaleByTime(t.staleTime)}var $i=function(e){Be(t,e);function t(s,n){var i;return i=e.call(this)||this,i.client=s,i.setOptions(n),i.bindMethods(),i.updateResult(),i}var r=t.prototype;return r.bindMethods=function(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)},r.setOptions=function(n){this.options=this.client.defaultMutationOptions(n)},r.onUnsubscribe=function(){if(!this.listeners.length){var n;(n=this.currentMutation)==null||n.removeObserver(this)}},r.onMutationUpdate=function(n){this.updateResult();var i={listeners:!0};n.type==="success"?i.onSuccess=!0:n.type==="error"&&(i.onError=!0),this.notify(i)},r.getCurrentResult=function(){return this.currentResult},r.reset=function(){this.currentMutation=void 0,this.updateResult(),this.notify({listeners:!0})},r.mutate=function(n,i){return this.mutateOptions=i,this.currentMutation&&this.currentMutation.removeObserver(this),this.currentMutation=this.client.getMutationCache().build(this.client,D({},this.options,{variables:typeof n<"u"?n:this.options.variables})),this.currentMutation.addObserver(this),this.currentMutation.execute()},r.updateResult=function(){var n=this.currentMutation?this.currentMutation.state:Us(),i=D({},n,{isLoading:n.status==="loading",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset});this.currentResult=i},r.notify=function(n){var i=this;B.batch(function(){i.mutateOptions&&(n.onSuccess?(i.mutateOptions.onSuccess==null||i.mutateOptions.onSuccess(i.currentResult.data,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(i.currentResult.data,null,i.currentResult.variables,i.currentResult.context)):n.onError&&(i.mutateOptions.onError==null||i.mutateOptions.onError(i.currentResult.error,i.currentResult.variables,i.currentResult.context),i.mutateOptions.onSettled==null||i.mutateOptions.onSettled(void 0,i.currentResult.error,i.currentResult.variables,i.currentResult.context))),n.listeners&&i.listeners.forEach(function(a){a(i.currentResult)})})},t}(He),Qi=Hn.unstable_batchedUpdates;B.setBatchNotifyFunction(Qi);var Vi=console;Ai(Vi);var Jr=M.createContext(void 0),Ms=M.createContext(!1);function qs(e){return e&&typeof window<"u"?(window.ReactQueryClientContext||(window.ReactQueryClientContext=Jr),window.ReactQueryClientContext):Jr}var Is=function(){var t=M.useContext(qs(M.useContext(Ms)));if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},Bi=function(t){var r=t.client,s=t.contextSharing,n=s===void 0?!1:s,i=t.children;M.useEffect(function(){return r.mount(),function(){r.unmount()}},[r]);var a=qs(n);return M.createElement(Ms.Provider,{value:n},M.createElement(a.Provider,{value:r},i))};function Hi(){var e=!1;return{clearReset:function(){e=!1},reset:function(){e=!0},isReset:function(){return e}}}var zi=M.createContext(Hi()),Ki=function(){return M.useContext(zi)};function $s(e,t,r){return typeof t=="function"?t.apply(void 0,r):typeof t=="boolean"?t:!!e}function Qs(e,t,r){var s=M.useRef(!1),n=M.useState(0),i=n[1],a=wi(e,t,r),o=Is(),c=M.useRef();c.current?c.current.setOptions(a):c.current=new $i(o,a);var u=c.current.getCurrentResult();M.useEffect(function(){s.current=!0;var m=c.current.subscribe(B.batchCalls(function(){s.current&&i(function(v){return v+1})}));return function(){s.current=!1,m()}},[]);var f=M.useCallback(function(m,v){c.current.mutate(m,v).catch(ee)},[]);if(u.error&&$s(void 0,c.current.options.useErrorBoundary,[u.error]))throw u.error;return D({},u,{mutate:f,mutateAsync:u.mutate})}function Wi(e,t){var r=M.useRef(!1),s=M.useState(0),n=s[1],i=Is(),a=Ki(),o=i.defaultQueryObserverOptions(e);o.optimisticResults=!0,o.onError&&(o.onError=B.batchCalls(o.onError)),o.onSuccess&&(o.onSuccess=B.batchCalls(o.onSuccess)),o.onSettled&&(o.onSettled=B.batchCalls(o.onSettled)),o.suspense&&(typeof o.staleTime!="number"&&(o.staleTime=1e3),o.cacheTime===0&&(o.cacheTime=1)),(o.suspense||o.useErrorBoundary)&&(a.isReset()||(o.retryOnMount=!1));var c=M.useState(function(){return new t(i,o)}),u=c[0],f=u.getOptimisticResult(o);if(M.useEffect(function(){r.current=!0,a.clearReset();var m=u.subscribe(B.batchCalls(function(){r.current&&n(function(v){return v+1})}));return u.updateResult(),function(){r.current=!1,m()}},[a,u]),M.useEffect(function(){u.setOptions(o,{listeners:!1})},[o,u]),o.suspense&&f.isLoading)throw u.fetchOptimistic(o).then(function(m){var v=m.data;o.onSuccess==null||o.onSuccess(v),o.onSettled==null||o.onSettled(v,null)}).catch(function(m){a.clearReset(),o.onError==null||o.onError(m),o.onSettled==null||o.onSettled(void 0,m)});if(f.isError&&!a.isReset()&&!f.isFetching&&$s(o.suspense,o.useErrorBoundary,[f.error,u.getCurrentQuery()]))throw f.error;return o.notifyOnChangeProps==="tracked"&&(f=u.trackResult(f,o)),f}function Vs(e,t,r){var s=mt(e,t,r);return Wi(s,qi)}let Ji={data:""},Gi=e=>typeof window=="object"?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Ji,Zi=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Xi=/\/\*[^]*?\*\/|  +/g,Gr=/\n+/g,Ae=(e,t)=>{let r="",s="",n="";for(let i in e){let a=e[i];i[0]=="@"?i[1]=="i"?r=i+" "+a+";":s+=i[1]=="f"?Ae(a,i):i+"{"+Ae(a,i[1]=="k"?"":t)+"}":typeof a=="object"?s+=Ae(a,t?t.replace(/([^,])+/g,o=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,c=>/&/.test(c)?c.replace(/&/g,o):o?o+" "+c:c)):i):a!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=Ae.p?Ae.p(i,a):i+":"+a+";")}return r+(t&&n?t+"{"+n+"}":n)+s},Se={},Bs=e=>{if(typeof e=="object"){let t="";for(let r in e)t+=r+Bs(e[r]);return t}return e},Yi=(e,t,r,s,n)=>{let i=Bs(e),a=Se[i]||(Se[i]=(c=>{let u=0,f=11;for(;u<c.length;)f=101*f+c.charCodeAt(u++)>>>0;return"go"+f})(i));if(!Se[a]){let c=i!==e?e:(u=>{let f,m,v=[{}];for(;f=Zi.exec(u.replace(Xi,""));)f[4]?v.shift():f[3]?(m=f[3].replace(Gr," ").trim(),v.unshift(v[0][m]=v[0][m]||{})):v[0][f[1]]=f[2].replace(Gr," ").trim();return v[0]})(e);Se[a]=Ae(n?{["@keyframes "+a]:c}:c,r?"":"."+a)}let o=r&&Se.g?Se.g:null;return r&&(Se.g=Se[a]),((c,u,f,m)=>{m?u.data=u.data.replace(m,c):u.data.indexOf(c)===-1&&(u.data=f?c+u.data:u.data+c)})(Se[a],t,s,o),a},ea=(e,t,r)=>e.reduce((s,n,i)=>{let a=t[i];if(a&&a.call){let o=a(r),c=o&&o.props&&o.props.className||/^go/.test(o)&&o;a=c?"."+c:o&&typeof o=="object"?o.props?"":Ae(o,""):o===!1?"":o}return s+n+(a??"")},"");function Lt(e){let t=this||{},r=e.call?e(t.p):e;return Yi(r.unshift?r.raw?ea(r,[].slice.call(arguments,1),t.p):r.reduce((s,n)=>Object.assign(s,n&&n.call?n(t.p):n),{}):r,Gi(t.target),t.g,t.o,t.k)}let Hs,dr,fr;Lt.bind({g:1});let Oe=Lt.bind({k:1});function ta(e,t,r,s){Ae.p=t,Hs=e,dr=r,fr=s}function Pe(e,t){let r=this||{};return function(){let s=arguments;function n(i,a){let o=Object.assign({},i),c=o.className||n.className;r.p=Object.assign({theme:dr&&dr()},o),r.o=/ *go\d+/.test(c),o.className=Lt.apply(r,s)+(c?" "+c:"");let u=e;return e[0]&&(u=o.as||e,delete o.as),fr&&u[0]&&fr(o),Hs(u,o)}return n}}var ra=e=>typeof e=="function",Ct=(e,t)=>ra(e)?e(t):e,sa=(()=>{let e=0;return()=>(++e).toString()})(),zs=(()=>{let e;return()=>{if(e===void 0&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),na=20,Ks=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,na)};case 1:return{...e,toasts:e.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:r}=t;return Ks(e,{type:e.toasts.find(i=>i.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(i=>i.id===s||s===void 0?{...i,dismissed:!0,visible:!1}:i)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},vt=[],De={toasts:[],pausedAt:void 0},Ie=e=>{De=Ks(De,e),vt.forEach(t=>{t(De)})},ia={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},aa=(e={})=>{let[t,r]=T.useState(De),s=T.useRef(De);T.useEffect(()=>(s.current!==De&&r(De),vt.push(r),()=>{let i=vt.indexOf(r);i>-1&&vt.splice(i,1)}),[]);let n=t.toasts.map(i=>{var a,o,c;return{...e,...e[i.type],...i,removeDelay:i.removeDelay||((a=e[i.type])==null?void 0:a.removeDelay)||(e==null?void 0:e.removeDelay),duration:i.duration||((o=e[i.type])==null?void 0:o.duration)||(e==null?void 0:e.duration)||ia[i.type],style:{...e.style,...(c=e[i.type])==null?void 0:c.style,...i.style}}});return{...t,toasts:n}},oa=(e,t="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...r,id:(r==null?void 0:r.id)||sa()}),lt=e=>(t,r)=>{let s=oa(t,e,r);return Ie({type:2,toast:s}),s.id},ae=(e,t)=>lt("blank")(e,t);ae.error=lt("error");ae.success=lt("success");ae.loading=lt("loading");ae.custom=lt("custom");ae.dismiss=e=>{Ie({type:3,toastId:e})};ae.remove=e=>Ie({type:4,toastId:e});ae.promise=(e,t,r)=>{let s=ae.loading(t.loading,{...r,...r==null?void 0:r.loading});return typeof e=="function"&&(e=e()),e.then(n=>{let i=t.success?Ct(t.success,n):void 0;return i?ae.success(i,{id:s,...r,...r==null?void 0:r.success}):ae.dismiss(s),n}).catch(n=>{let i=t.error?Ct(t.error,n):void 0;i?ae.error(i,{id:s,...r,...r==null?void 0:r.error}):ae.dismiss(s)}),e};var la=(e,t)=>{Ie({type:1,toast:{id:e,height:t}})},ua=()=>{Ie({type:5,time:Date.now()})},et=new Map,ca=1e3,da=(e,t=ca)=>{if(et.has(e))return;let r=setTimeout(()=>{et.delete(e),Ie({type:4,toastId:e})},t);et.set(e,r)},fa=e=>{let{toasts:t,pausedAt:r}=aa(e);T.useEffect(()=>{if(r)return;let i=Date.now(),a=t.map(o=>{if(o.duration===1/0)return;let c=(o.duration||0)+o.pauseDuration-(i-o.createdAt);if(c<0){o.visible&&ae.dismiss(o.id);return}return setTimeout(()=>ae.dismiss(o.id),c)});return()=>{a.forEach(o=>o&&clearTimeout(o))}},[t,r]);let s=T.useCallback(()=>{r&&Ie({type:6,time:Date.now()})},[r]),n=T.useCallback((i,a)=>{let{reverseOrder:o=!1,gutter:c=8,defaultPosition:u}=a||{},f=t.filter(b=>(b.position||u)===(i.position||u)&&b.height),m=f.findIndex(b=>b.id===i.id),v=f.filter((b,g)=>g<m&&b.visible).length;return f.filter(b=>b.visible).slice(...o?[v+1]:[0,v]).reduce((b,g)=>b+(g.height||0)+c,0)},[t]);return T.useEffect(()=>{t.forEach(i=>{if(i.dismissed)da(i.id,i.removeDelay);else{let a=et.get(i.id);a&&(clearTimeout(a),et.delete(i.id))}})},[t]),{toasts:t,handlers:{updateHeight:la,startPause:ua,endPause:s,calculateOffset:n}}},ha=Oe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,ma=Oe`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,pa=Oe`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,ya=Pe("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${ha} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${ma} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${pa} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,va=Oe`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,ga=Pe("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${va} 1s linear infinite;
`,xa=Oe`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,ba=Oe`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,wa=Pe("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${xa} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${ba} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,ja=Pe("div")`
  position: absolute;
`,Sa=Pe("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Na=Oe`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Ea=Pe("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Na} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Oa=({toast:e})=>{let{icon:t,type:r,iconTheme:s}=e;return t!==void 0?typeof t=="string"?T.createElement(Ea,null,t):t:r==="blank"?null:T.createElement(Sa,null,T.createElement(ga,{...s}),r!=="loading"&&T.createElement(ja,null,r==="error"?T.createElement(ya,{...s}):T.createElement(wa,{...s})))},Ra=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,_a=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,Ca="0%{opacity:0;} 100%{opacity:1;}",Aa="0%{opacity:1;} 100%{opacity:0;}",Fa=Pe("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,Pa=Pe("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,Ta=(e,t)=>{let r=e.includes("top")?1:-1,[s,n]=zs()?[Ca,Aa]:[Ra(r),_a(r)];return{animation:t?`${Oe(s)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Oe(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},Da=T.memo(({toast:e,position:t,style:r,children:s})=>{let n=e.height?Ta(e.position||t||"top-center",e.visible):{opacity:0},i=T.createElement(Oa,{toast:e}),a=T.createElement(Pa,{...e.ariaProps},Ct(e.message,e));return T.createElement(Fa,{className:e.className,style:{...n,...r,...e.style}},typeof s=="function"?s({icon:i,message:a}):T.createElement(T.Fragment,null,i,a))});ta(T.createElement);var ka=({id:e,className:t,style:r,onHeightUpdate:s,children:n})=>{let i=T.useCallback(a=>{if(a){let o=()=>{let c=a.getBoundingClientRect().height;s(e,c)};o(),new MutationObserver(o).observe(a,{subtree:!0,childList:!0,characterData:!0})}},[e,s]);return T.createElement("div",{ref:i,className:t,style:r},n)},La=(e,t)=>{let r=e.includes("top"),s=r?{top:0}:{bottom:0},n=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:zs()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(r?1:-1)}px)`,...s,...n}},Ua=Lt`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,ht=16,Ma=({reverseOrder:e,position:t="top-center",toastOptions:r,gutter:s,children:n,containerStyle:i,containerClassName:a})=>{let{toasts:o,handlers:c}=fa(r);return T.createElement("div",{id:"_rht_toaster",style:{position:"fixed",zIndex:9999,top:ht,left:ht,right:ht,bottom:ht,pointerEvents:"none",...i},className:a,onMouseEnter:c.startPause,onMouseLeave:c.endPause},o.map(u=>{let f=u.position||t,m=c.calculateOffset(u,{reverseOrder:e,gutter:s,defaultPosition:t}),v=La(f,m);return T.createElement(ka,{id:u.id,key:u.id,onHeightUpdate:c.updateHeight,className:u.visible?Ua:"",style:v},u.type==="custom"?Ct(u.message,u):n?n(u):T.createElement(Da,{toast:u,position:f}))}))},pe=ae;const qa={},Zr=e=>{let t;const r=new Set,s=(f,m)=>{const v=typeof f=="function"?f(t):f;if(!Object.is(v,t)){const b=t;t=m??(typeof v!="object"||v===null)?v:Object.assign({},t,v),r.forEach(g=>g(t,b))}},n=()=>t,c={setState:s,getState:n,getInitialState:()=>u,subscribe:f=>(r.add(f),()=>r.delete(f)),destroy:()=>{(qa?"production":void 0)!=="production"&&console.warn("[DEPRECATED] The `destroy` method will be unsupported in a future version. Instead use unsubscribe function returned by subscribe. Everything will be garbage-collected if store is garbage-collected."),r.clear()}},u=t=e(s,n,c);return c},Ia=e=>e?Zr(e):Zr;var Ws={exports:{}},Js={},Gs={exports:{}},Zs={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ve=T;function $a(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Qa=typeof Object.is=="function"?Object.is:$a,Va=Ve.useState,Ba=Ve.useEffect,Ha=Ve.useLayoutEffect,za=Ve.useDebugValue;function Ka(e,t){var r=t(),s=Va({inst:{value:r,getSnapshot:t}}),n=s[0].inst,i=s[1];return Ha(function(){n.value=r,n.getSnapshot=t,Xt(n)&&i({inst:n})},[e,r,t]),Ba(function(){return Xt(n)&&i({inst:n}),e(function(){Xt(n)&&i({inst:n})})},[e]),za(r),r}function Xt(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!Qa(e,r)}catch{return!0}}function Wa(e,t){return t()}var Ja=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?Wa:Ka;Zs.useSyncExternalStore=Ve.useSyncExternalStore!==void 0?Ve.useSyncExternalStore:Ja;Gs.exports=Zs;var Ga=Gs.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ut=T,Za=Ga;function Xa(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ya=typeof Object.is=="function"?Object.is:Xa,eo=Za.useSyncExternalStore,to=Ut.useRef,ro=Ut.useEffect,so=Ut.useMemo,no=Ut.useDebugValue;Js.useSyncExternalStoreWithSelector=function(e,t,r,s,n){var i=to(null);if(i.current===null){var a={hasValue:!1,value:null};i.current=a}else a=i.current;i=so(function(){function c(b){if(!u){if(u=!0,f=b,b=s(b),n!==void 0&&a.hasValue){var g=a.value;if(n(g,b))return m=g}return m=b}if(g=m,Ya(f,b))return g;var h=s(b);return n!==void 0&&n(g,h)?(f=b,g):(f=b,m=h)}var u=!1,f,m,v=r===void 0?null:r;return[function(){return c(t())},v===null?void 0:function(){return c(v())}]},[t,r,s,n]);var o=eo(e,i[0],i[1]);return ro(function(){a.hasValue=!0,a.value=o},[o]),no(o),o};Ws.exports=Js;var io=Ws.exports;const ao=zn(io),Xs={},{useDebugValue:oo}=M,{useSyncExternalStoreWithSelector:lo}=ao;let Xr=!1;const uo=e=>e;function co(e,t=uo,r){(Xs?"production":void 0)!=="production"&&r&&!Xr&&(console.warn("[DEPRECATED] Use `createWithEqualityFn` instead of `create` or use `useStoreWithEqualityFn` instead of `useStore`. They can be imported from 'zustand/traditional'. https://github.com/pmndrs/zustand/discussions/1937"),Xr=!0);const s=lo(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,r);return oo(s),s}const fo=e=>{(Xs?"production":void 0)!=="production"&&typeof e!="function"&&console.warn("[DEPRECATED] Passing a vanilla store will be unsupported in a future version. Instead use `import { useStore } from 'zustand'`.");const t=typeof e=="function"?Ia(e):e,r=(s,n)=>co(t,s,n);return Object.assign(r,t),r},ho=e=>fo,mo={};function po(e,t){let r;try{r=e()}catch{return}return{getItem:n=>{var i;const a=c=>c===null?null:JSON.parse(c,void 0),o=(i=r.getItem(n))!=null?i:null;return o instanceof Promise?o.then(a):a(o)},setItem:(n,i)=>r.setItem(n,JSON.stringify(i,void 0)),removeItem:n=>r.removeItem(n)}}const nt=e=>t=>{try{const r=e(t);return r instanceof Promise?r:{then(s){return nt(s)(r)},catch(s){return this}}}catch(r){return{then(s){return this},catch(s){return nt(s)(r)}}}},yo=(e,t)=>(r,s,n)=>{let i={getStorage:()=>localStorage,serialize:JSON.stringify,deserialize:JSON.parse,partialize:w=>w,version:0,merge:(w,O)=>({...O,...w}),...t},a=!1;const o=new Set,c=new Set;let u;try{u=i.getStorage()}catch{}if(!u)return e((...w)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...w)},s,n);const f=nt(i.serialize),m=()=>{const w=i.partialize({...s()});let O;const j=f({state:w,version:i.version}).then(N=>u.setItem(i.name,N)).catch(N=>{O=N});if(O)throw O;return j},v=n.setState;n.setState=(w,O)=>{v(w,O),m()};const b=e((...w)=>{r(...w),m()},s,n);let g;const h=()=>{var w;if(!u)return;a=!1,o.forEach(j=>j(s()));const O=((w=i.onRehydrateStorage)==null?void 0:w.call(i,s()))||void 0;return nt(u.getItem.bind(u))(i.name).then(j=>{if(j)return i.deserialize(j)}).then(j=>{if(j)if(typeof j.version=="number"&&j.version!==i.version){if(i.migrate)return i.migrate(j.state,j.version);console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return j.state}).then(j=>{var N;return g=i.merge(j,(N=s())!=null?N:b),r(g,!0),m()}).then(()=>{O==null||O(g,void 0),a=!0,c.forEach(j=>j(g))}).catch(j=>{O==null||O(void 0,j)})};return n.persist={setOptions:w=>{i={...i,...w},w.getStorage&&(u=w.getStorage())},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>h(),hasHydrated:()=>a,onHydrate:w=>(o.add(w),()=>{o.delete(w)}),onFinishHydration:w=>(c.add(w),()=>{c.delete(w)})},h(),g||b},vo=(e,t)=>(r,s,n)=>{let i={storage:po(()=>localStorage),partialize:h=>h,version:0,merge:(h,w)=>({...w,...h}),...t},a=!1;const o=new Set,c=new Set;let u=i.storage;if(!u)return e((...h)=>{console.warn(`[zustand persist middleware] Unable to update item '${i.name}', the given storage is currently unavailable.`),r(...h)},s,n);const f=()=>{const h=i.partialize({...s()});return u.setItem(i.name,{state:h,version:i.version})},m=n.setState;n.setState=(h,w)=>{m(h,w),f()};const v=e((...h)=>{r(...h),f()},s,n);n.getInitialState=()=>v;let b;const g=()=>{var h,w;if(!u)return;a=!1,o.forEach(j=>{var N;return j((N=s())!=null?N:v)});const O=((w=i.onRehydrateStorage)==null?void 0:w.call(i,(h=s())!=null?h:v))||void 0;return nt(u.getItem.bind(u))(i.name).then(j=>{if(j)if(typeof j.version=="number"&&j.version!==i.version){if(i.migrate)return[!0,i.migrate(j.state,j.version)];console.error("State loaded from storage couldn't be migrated since no migrate function was provided")}else return[!1,j.state];return[!1,void 0]}).then(j=>{var N;const[k,A]=j;if(b=i.merge(A,(N=s())!=null?N:v),r(b,!0),k)return f()}).then(()=>{O==null||O(b,void 0),b=s(),a=!0,c.forEach(j=>j(b))}).catch(j=>{O==null||O(void 0,j)})};return n.persist={setOptions:h=>{i={...i,...h},h.storage&&(u=h.storage)},clearStorage:()=>{u==null||u.removeItem(i.name)},getOptions:()=>i,rehydrate:()=>g(),hasHydrated:()=>a,onHydrate:h=>(o.add(h),()=>{o.delete(h)}),onFinishHydration:h=>(c.add(h),()=>{c.delete(h)})},i.skipHydration||g(),b||v},go=(e,t)=>"getStorage"in t||"serialize"in t||"deserialize"in t?((mo?"production":void 0)!=="production"&&console.warn("[DEPRECATED] `getStorage`, `serialize` and `deserialize` options are deprecated. Use `storage` option instead."),yo(e,t)):vo(e,t),xo=go,Re=ho()(xo((e,t)=>({user:null,tokens:null,isAuthenticated:!1,isLoading:!1,login:(r,s)=>{e({user:r,tokens:s,isAuthenticated:!0,isLoading:!1})},logout:()=>{e({user:null,tokens:null,isAuthenticated:!1,isLoading:!1}),localStorage.removeItem("auth-storage"),window.location.pathname.includes("/login")||(window.location.href="/login")},updateUser:r=>{const s=t().user;s&&e({user:{...s,...r}})},updateTokens:r=>{e({tokens:r})},setLoading:r=>{e({isLoading:r})},clearError:()=>{}}),{name:"auth-storage",partialize:e=>({user:e.user,tokens:e.tokens,isAuthenticated:e.isAuthenticated})}));function Ys(e,t){return function(){return e.apply(t,arguments)}}const{toString:bo}=Object.prototype,{getPrototypeOf:Sr}=Object,{iterator:Mt,toStringTag:en}=Symbol,qt=(e=>t=>{const r=bo.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),ge=e=>(e=e.toLowerCase(),t=>qt(t)===e),It=e=>t=>typeof t===e,{isArray:ze}=Array,it=It("undefined");function wo(e){return e!==null&&!it(e)&&e.constructor!==null&&!it(e.constructor)&&ce(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const tn=ge("ArrayBuffer");function jo(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&tn(e.buffer),t}const So=It("string"),ce=It("function"),rn=It("number"),$t=e=>e!==null&&typeof e=="object",No=e=>e===!0||e===!1,gt=e=>{if(qt(e)!=="object")return!1;const t=Sr(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(en in e)&&!(Mt in e)},Eo=ge("Date"),Oo=ge("File"),Ro=ge("Blob"),_o=ge("FileList"),Co=e=>$t(e)&&ce(e.pipe),Ao=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ce(e.append)&&((t=qt(e))==="formdata"||t==="object"&&ce(e.toString)&&e.toString()==="[object FormData]"))},Fo=ge("URLSearchParams"),[Po,To,Do,ko]=["ReadableStream","Request","Response","Headers"].map(ge),Lo=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ut(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let s,n;if(typeof e!="object"&&(e=[e]),ze(e))for(s=0,n=e.length;s<n;s++)t.call(null,e[s],s,e);else{const i=r?Object.getOwnPropertyNames(e):Object.keys(e),a=i.length;let o;for(s=0;s<a;s++)o=i[s],t.call(null,e[o],o,e)}}function sn(e,t){t=t.toLowerCase();const r=Object.keys(e);let s=r.length,n;for(;s-- >0;)if(n=r[s],t===n.toLowerCase())return n;return null}const ke=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,nn=e=>!it(e)&&e!==ke;function hr(){const{caseless:e}=nn(this)&&this||{},t={},r=(s,n)=>{const i=e&&sn(t,n)||n;gt(t[i])&&gt(s)?t[i]=hr(t[i],s):gt(s)?t[i]=hr({},s):ze(s)?t[i]=s.slice():t[i]=s};for(let s=0,n=arguments.length;s<n;s++)arguments[s]&&ut(arguments[s],r);return t}const Uo=(e,t,r,{allOwnKeys:s}={})=>(ut(t,(n,i)=>{r&&ce(n)?e[i]=Ys(n,r):e[i]=n},{allOwnKeys:s}),e),Mo=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),qo=(e,t,r,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},Io=(e,t,r,s)=>{let n,i,a;const o={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),i=n.length;i-- >0;)a=n[i],(!s||s(a,e,t))&&!o[a]&&(t[a]=e[a],o[a]=!0);e=r!==!1&&Sr(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},$o=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const s=e.indexOf(t,r);return s!==-1&&s===r},Qo=e=>{if(!e)return null;if(ze(e))return e;let t=e.length;if(!rn(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},Vo=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Sr(Uint8Array)),Bo=(e,t)=>{const s=(e&&e[Mt]).call(e);let n;for(;(n=s.next())&&!n.done;){const i=n.value;t.call(e,i[0],i[1])}},Ho=(e,t)=>{let r;const s=[];for(;(r=e.exec(t))!==null;)s.push(r);return s},zo=ge("HTMLFormElement"),Ko=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,s,n){return s.toUpperCase()+n}),Yr=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Wo=ge("RegExp"),an=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),s={};ut(r,(n,i)=>{let a;(a=t(n,i,e))!==!1&&(s[i]=a||n)}),Object.defineProperties(e,s)},Jo=e=>{an(e,(t,r)=>{if(ce(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const s=e[r];if(ce(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Go=(e,t)=>{const r={},s=n=>{n.forEach(i=>{r[i]=!0})};return ze(e)?s(e):s(String(e).split(t)),r},Zo=()=>{},Xo=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Yo(e){return!!(e&&ce(e.append)&&e[en]==="FormData"&&e[Mt])}const el=e=>{const t=new Array(10),r=(s,n)=>{if($t(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[n]=s;const i=ze(s)?[]:{};return ut(s,(a,o)=>{const c=r(a,n+1);!it(c)&&(i[o]=c)}),t[n]=void 0,i}}return s};return r(e,0)},tl=ge("AsyncFunction"),rl=e=>e&&($t(e)||ce(e))&&ce(e.then)&&ce(e.catch),on=((e,t)=>e?setImmediate:t?((r,s)=>(ke.addEventListener("message",({source:n,data:i})=>{n===ke&&i===r&&s.length&&s.shift()()},!1),n=>{s.push(n),ke.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",ce(ke.postMessage)),sl=typeof queueMicrotask<"u"?queueMicrotask.bind(ke):typeof process<"u"&&process.nextTick||on,nl=e=>e!=null&&ce(e[Mt]),y={isArray:ze,isArrayBuffer:tn,isBuffer:wo,isFormData:Ao,isArrayBufferView:jo,isString:So,isNumber:rn,isBoolean:No,isObject:$t,isPlainObject:gt,isReadableStream:Po,isRequest:To,isResponse:Do,isHeaders:ko,isUndefined:it,isDate:Eo,isFile:Oo,isBlob:Ro,isRegExp:Wo,isFunction:ce,isStream:Co,isURLSearchParams:Fo,isTypedArray:Vo,isFileList:_o,forEach:ut,merge:hr,extend:Uo,trim:Lo,stripBOM:Mo,inherits:qo,toFlatObject:Io,kindOf:qt,kindOfTest:ge,endsWith:$o,toArray:Qo,forEachEntry:Bo,matchAll:Ho,isHTMLForm:zo,hasOwnProperty:Yr,hasOwnProp:Yr,reduceDescriptors:an,freezeMethods:Jo,toObjectSet:Go,toCamelCase:Ko,noop:Zo,toFiniteNumber:Xo,findKey:sn,global:ke,isContextDefined:nn,isSpecCompliantForm:Yo,toJSONObject:el,isAsyncFn:tl,isThenable:rl,setImmediate:on,asap:sl,isIterable:nl};function F(e,t,r,s,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),s&&(this.request=s),n&&(this.response=n,this.status=n.status?n.status:null)}y.inherits(F,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:y.toJSONObject(this.config),code:this.code,status:this.status}}});const ln=F.prototype,un={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{un[e]={value:e}});Object.defineProperties(F,un);Object.defineProperty(ln,"isAxiosError",{value:!0});F.from=(e,t,r,s,n,i)=>{const a=Object.create(ln);return y.toFlatObject(e,a,function(c){return c!==Error.prototype},o=>o!=="isAxiosError"),F.call(a,e.message,t,r,s,n),a.cause=e,a.name=e.name,i&&Object.assign(a,i),a};const il=null;function mr(e){return y.isPlainObject(e)||y.isArray(e)}function cn(e){return y.endsWith(e,"[]")?e.slice(0,-2):e}function es(e,t,r){return e?e.concat(t).map(function(n,i){return n=cn(n),!r&&i?"["+n+"]":n}).join(r?".":""):t}function al(e){return y.isArray(e)&&!e.some(mr)}const ol=y.toFlatObject(y,{},null,function(t){return/^is[A-Z]/.test(t)});function Qt(e,t,r){if(!y.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=y.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(h,w){return!y.isUndefined(w[h])});const s=r.metaTokens,n=r.visitor||f,i=r.dots,a=r.indexes,c=(r.Blob||typeof Blob<"u"&&Blob)&&y.isSpecCompliantForm(t);if(!y.isFunction(n))throw new TypeError("visitor must be a function");function u(g){if(g===null)return"";if(y.isDate(g))return g.toISOString();if(!c&&y.isBlob(g))throw new F("Blob is not supported. Use a Buffer instead.");return y.isArrayBuffer(g)||y.isTypedArray(g)?c&&typeof Blob=="function"?new Blob([g]):Buffer.from(g):g}function f(g,h,w){let O=g;if(g&&!w&&typeof g=="object"){if(y.endsWith(h,"{}"))h=s?h:h.slice(0,-2),g=JSON.stringify(g);else if(y.isArray(g)&&al(g)||(y.isFileList(g)||y.endsWith(h,"[]"))&&(O=y.toArray(g)))return h=cn(h),O.forEach(function(N,k){!(y.isUndefined(N)||N===null)&&t.append(a===!0?es([h],k,i):a===null?h:h+"[]",u(N))}),!1}return mr(g)?!0:(t.append(es(w,h,i),u(g)),!1)}const m=[],v=Object.assign(ol,{defaultVisitor:f,convertValue:u,isVisitable:mr});function b(g,h){if(!y.isUndefined(g)){if(m.indexOf(g)!==-1)throw Error("Circular reference detected in "+h.join("."));m.push(g),y.forEach(g,function(O,j){(!(y.isUndefined(O)||O===null)&&n.call(t,O,y.isString(j)?j.trim():j,h,v))===!0&&b(O,h?h.concat(j):[j])}),m.pop()}}if(!y.isObject(e))throw new TypeError("data must be an object");return b(e),t}function ts(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function Nr(e,t){this._pairs=[],e&&Qt(e,this,t)}const dn=Nr.prototype;dn.append=function(t,r){this._pairs.push([t,r])};dn.toString=function(t){const r=t?function(s){return t.call(this,s,ts)}:ts;return this._pairs.map(function(n){return r(n[0])+"="+r(n[1])},"").join("&")};function ll(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function fn(e,t,r){if(!t)return e;const s=r&&r.encode||ll;y.isFunction(r)&&(r={serialize:r});const n=r&&r.serialize;let i;if(n?i=n(t,r):i=y.isURLSearchParams(t)?t.toString():new Nr(t,r).toString(s),i){const a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class rs{constructor(){this.handlers=[]}use(t,r,s){return this.handlers.push({fulfilled:t,rejected:r,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){y.forEach(this.handlers,function(s){s!==null&&t(s)})}}const hn={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ul=typeof URLSearchParams<"u"?URLSearchParams:Nr,cl=typeof FormData<"u"?FormData:null,dl=typeof Blob<"u"?Blob:null,fl={isBrowser:!0,classes:{URLSearchParams:ul,FormData:cl,Blob:dl},protocols:["http","https","file","blob","url","data"]},Er=typeof window<"u"&&typeof document<"u",pr=typeof navigator=="object"&&navigator||void 0,hl=Er&&(!pr||["ReactNative","NativeScript","NS"].indexOf(pr.product)<0),ml=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",pl=Er&&window.location.href||"http://localhost",yl=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Er,hasStandardBrowserEnv:hl,hasStandardBrowserWebWorkerEnv:ml,navigator:pr,origin:pl},Symbol.toStringTag,{value:"Module"})),se={...yl,...fl};function vl(e,t){return Qt(e,new se.classes.URLSearchParams,Object.assign({visitor:function(r,s,n,i){return se.isNode&&y.isBuffer(r)?(this.append(s,r.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function gl(e){return y.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function xl(e){const t={},r=Object.keys(e);let s;const n=r.length;let i;for(s=0;s<n;s++)i=r[s],t[i]=e[i];return t}function mn(e){function t(r,s,n,i){let a=r[i++];if(a==="__proto__")return!0;const o=Number.isFinite(+a),c=i>=r.length;return a=!a&&y.isArray(n)?n.length:a,c?(y.hasOwnProp(n,a)?n[a]=[n[a],s]:n[a]=s,!o):((!n[a]||!y.isObject(n[a]))&&(n[a]=[]),t(r,s,n[a],i)&&y.isArray(n[a])&&(n[a]=xl(n[a])),!o)}if(y.isFormData(e)&&y.isFunction(e.entries)){const r={};return y.forEachEntry(e,(s,n)=>{t(gl(s),n,r,0)}),r}return null}function bl(e,t,r){if(y.isString(e))try{return(t||JSON.parse)(e),y.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(r||JSON.stringify)(e)}const ct={transitional:hn,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const s=r.getContentType()||"",n=s.indexOf("application/json")>-1,i=y.isObject(t);if(i&&y.isHTMLForm(t)&&(t=new FormData(t)),y.isFormData(t))return n?JSON.stringify(mn(t)):t;if(y.isArrayBuffer(t)||y.isBuffer(t)||y.isStream(t)||y.isFile(t)||y.isBlob(t)||y.isReadableStream(t))return t;if(y.isArrayBufferView(t))return t.buffer;if(y.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let o;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return vl(t,this.formSerializer).toString();if((o=y.isFileList(t))||s.indexOf("multipart/form-data")>-1){const c=this.env&&this.env.FormData;return Qt(o?{"files[]":t}:t,c&&new c,this.formSerializer)}}return i||n?(r.setContentType("application/json",!1),bl(t)):t}],transformResponse:[function(t){const r=this.transitional||ct.transitional,s=r&&r.forcedJSONParsing,n=this.responseType==="json";if(y.isResponse(t)||y.isReadableStream(t))return t;if(t&&y.isString(t)&&(s&&!this.responseType||n)){const a=!(r&&r.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(o){if(a)throw o.name==="SyntaxError"?F.from(o,F.ERR_BAD_RESPONSE,this,null,this.response):o}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:se.classes.FormData,Blob:se.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};y.forEach(["delete","get","head","post","put","patch"],e=>{ct.headers[e]={}});const wl=y.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),jl=e=>{const t={};let r,s,n;return e&&e.split(`
`).forEach(function(a){n=a.indexOf(":"),r=a.substring(0,n).trim().toLowerCase(),s=a.substring(n+1).trim(),!(!r||t[r]&&wl[r])&&(r==="set-cookie"?t[r]?t[r].push(s):t[r]=[s]:t[r]=t[r]?t[r]+", "+s:s)}),t},ss=Symbol("internals");function Je(e){return e&&String(e).trim().toLowerCase()}function xt(e){return e===!1||e==null?e:y.isArray(e)?e.map(xt):String(e)}function Sl(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=r.exec(e);)t[s[1]]=s[2];return t}const Nl=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function Yt(e,t,r,s,n){if(y.isFunction(s))return s.call(this,t,r);if(n&&(t=r),!!y.isString(t)){if(y.isString(s))return t.indexOf(s)!==-1;if(y.isRegExp(s))return s.test(t)}}function El(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,s)=>r.toUpperCase()+s)}function Ol(e,t){const r=y.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+r,{value:function(n,i,a){return this[s].call(this,t,n,i,a)},configurable:!0})})}let de=class{constructor(t){t&&this.set(t)}set(t,r,s){const n=this;function i(o,c,u){const f=Je(c);if(!f)throw new Error("header name must be a non-empty string");const m=y.findKey(n,f);(!m||n[m]===void 0||u===!0||u===void 0&&n[m]!==!1)&&(n[m||c]=xt(o))}const a=(o,c)=>y.forEach(o,(u,f)=>i(u,f,c));if(y.isPlainObject(t)||t instanceof this.constructor)a(t,r);else if(y.isString(t)&&(t=t.trim())&&!Nl(t))a(jl(t),r);else if(y.isObject(t)&&y.isIterable(t)){let o={},c,u;for(const f of t){if(!y.isArray(f))throw TypeError("Object iterator must return a key-value pair");o[u=f[0]]=(c=o[u])?y.isArray(c)?[...c,f[1]]:[c,f[1]]:f[1]}a(o,r)}else t!=null&&i(r,t,s);return this}get(t,r){if(t=Je(t),t){const s=y.findKey(this,t);if(s){const n=this[s];if(!r)return n;if(r===!0)return Sl(n);if(y.isFunction(r))return r.call(this,n,s);if(y.isRegExp(r))return r.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=Je(t),t){const s=y.findKey(this,t);return!!(s&&this[s]!==void 0&&(!r||Yt(this,this[s],s,r)))}return!1}delete(t,r){const s=this;let n=!1;function i(a){if(a=Je(a),a){const o=y.findKey(s,a);o&&(!r||Yt(s,s[o],o,r))&&(delete s[o],n=!0)}}return y.isArray(t)?t.forEach(i):i(t),n}clear(t){const r=Object.keys(this);let s=r.length,n=!1;for(;s--;){const i=r[s];(!t||Yt(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){const r=this,s={};return y.forEach(this,(n,i)=>{const a=y.findKey(s,i);if(a){r[a]=xt(n),delete r[i];return}const o=t?El(i):String(i).trim();o!==i&&delete r[i],r[o]=xt(n),s[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return y.forEach(this,(s,n)=>{s!=null&&s!==!1&&(r[n]=t&&y.isArray(s)?s.join(", "):s)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const s=new this(t);return r.forEach(n=>s.set(n)),s}static accessor(t){const s=(this[ss]=this[ss]={accessors:{}}).accessors,n=this.prototype;function i(a){const o=Je(a);s[o]||(Ol(n,a),s[o]=!0)}return y.isArray(t)?t.forEach(i):i(t),this}};de.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);y.reduceDescriptors(de.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[r]=s}}});y.freezeMethods(de);function er(e,t){const r=this||ct,s=t||r,n=de.from(s.headers);let i=s.data;return y.forEach(e,function(o){i=o.call(r,i,n.normalize(),t?t.status:void 0)}),n.normalize(),i}function pn(e){return!!(e&&e.__CANCEL__)}function Ke(e,t,r){F.call(this,e??"canceled",F.ERR_CANCELED,t,r),this.name="CanceledError"}y.inherits(Ke,F,{__CANCEL__:!0});function yn(e,t,r){const s=r.config.validateStatus;!r.status||!s||s(r.status)?e(r):t(new F("Request failed with status code "+r.status,[F.ERR_BAD_REQUEST,F.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function Rl(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function _l(e,t){e=e||10;const r=new Array(e),s=new Array(e);let n=0,i=0,a;return t=t!==void 0?t:1e3,function(c){const u=Date.now(),f=s[i];a||(a=u),r[n]=c,s[n]=u;let m=i,v=0;for(;m!==n;)v+=r[m++],m=m%e;if(n=(n+1)%e,n===i&&(i=(i+1)%e),u-a<t)return;const b=f&&u-f;return b?Math.round(v*1e3/b):void 0}}function Cl(e,t){let r=0,s=1e3/t,n,i;const a=(u,f=Date.now())=>{r=f,n=null,i&&(clearTimeout(i),i=null),e.apply(null,u)};return[(...u)=>{const f=Date.now(),m=f-r;m>=s?a(u,f):(n=u,i||(i=setTimeout(()=>{i=null,a(n)},s-m)))},()=>n&&a(n)]}const At=(e,t,r=3)=>{let s=0;const n=_l(50,250);return Cl(i=>{const a=i.loaded,o=i.lengthComputable?i.total:void 0,c=a-s,u=n(c),f=a<=o;s=a;const m={loaded:a,total:o,progress:o?a/o:void 0,bytes:c,rate:u||void 0,estimated:u&&o&&f?(o-a)/u:void 0,event:i,lengthComputable:o!=null,[t?"download":"upload"]:!0};e(m)},r)},ns=(e,t)=>{const r=e!=null;return[s=>t[0]({lengthComputable:r,total:e,loaded:s}),t[1]]},is=e=>(...t)=>y.asap(()=>e(...t)),Al=se.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,se.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(se.origin),se.navigator&&/(msie|trident)/i.test(se.navigator.userAgent)):()=>!0,Fl=se.hasStandardBrowserEnv?{write(e,t,r,s,n,i){const a=[e+"="+encodeURIComponent(t)];y.isNumber(r)&&a.push("expires="+new Date(r).toGMTString()),y.isString(s)&&a.push("path="+s),y.isString(n)&&a.push("domain="+n),i===!0&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Pl(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Tl(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function vn(e,t,r){let s=!Pl(t);return e&&(s||r==!1)?Tl(e,t):t}const as=e=>e instanceof de?{...e}:e;function qe(e,t){t=t||{};const r={};function s(u,f,m,v){return y.isPlainObject(u)&&y.isPlainObject(f)?y.merge.call({caseless:v},u,f):y.isPlainObject(f)?y.merge({},f):y.isArray(f)?f.slice():f}function n(u,f,m,v){if(y.isUndefined(f)){if(!y.isUndefined(u))return s(void 0,u,m,v)}else return s(u,f,m,v)}function i(u,f){if(!y.isUndefined(f))return s(void 0,f)}function a(u,f){if(y.isUndefined(f)){if(!y.isUndefined(u))return s(void 0,u)}else return s(void 0,f)}function o(u,f,m){if(m in t)return s(u,f);if(m in e)return s(void 0,u)}const c={url:i,method:i,data:i,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:o,headers:(u,f,m)=>n(as(u),as(f),m,!0)};return y.forEach(Object.keys(Object.assign({},e,t)),function(f){const m=c[f]||n,v=m(e[f],t[f],f);y.isUndefined(v)&&m!==o||(r[f]=v)}),r}const gn=e=>{const t=qe({},e);let{data:r,withXSRFToken:s,xsrfHeaderName:n,xsrfCookieName:i,headers:a,auth:o}=t;t.headers=a=de.from(a),t.url=fn(vn(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),o&&a.set("Authorization","Basic "+btoa((o.username||"")+":"+(o.password?unescape(encodeURIComponent(o.password)):"")));let c;if(y.isFormData(r)){if(se.hasStandardBrowserEnv||se.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if((c=a.getContentType())!==!1){const[u,...f]=c?c.split(";").map(m=>m.trim()).filter(Boolean):[];a.setContentType([u||"multipart/form-data",...f].join("; "))}}if(se.hasStandardBrowserEnv&&(s&&y.isFunction(s)&&(s=s(t)),s||s!==!1&&Al(t.url))){const u=n&&i&&Fl.read(i);u&&a.set(n,u)}return t},Dl=typeof XMLHttpRequest<"u",kl=Dl&&function(e){return new Promise(function(r,s){const n=gn(e);let i=n.data;const a=de.from(n.headers).normalize();let{responseType:o,onUploadProgress:c,onDownloadProgress:u}=n,f,m,v,b,g;function h(){b&&b(),g&&g(),n.cancelToken&&n.cancelToken.unsubscribe(f),n.signal&&n.signal.removeEventListener("abort",f)}let w=new XMLHttpRequest;w.open(n.method.toUpperCase(),n.url,!0),w.timeout=n.timeout;function O(){if(!w)return;const N=de.from("getAllResponseHeaders"in w&&w.getAllResponseHeaders()),A={data:!o||o==="text"||o==="json"?w.responseText:w.response,status:w.status,statusText:w.statusText,headers:N,config:e,request:w};yn(function(H){r(H),h()},function(H){s(H),h()},A),w=null}"onloadend"in w?w.onloadend=O:w.onreadystatechange=function(){!w||w.readyState!==4||w.status===0&&!(w.responseURL&&w.responseURL.indexOf("file:")===0)||setTimeout(O)},w.onabort=function(){w&&(s(new F("Request aborted",F.ECONNABORTED,e,w)),w=null)},w.onerror=function(){s(new F("Network Error",F.ERR_NETWORK,e,w)),w=null},w.ontimeout=function(){let k=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const A=n.transitional||hn;n.timeoutErrorMessage&&(k=n.timeoutErrorMessage),s(new F(k,A.clarifyTimeoutError?F.ETIMEDOUT:F.ECONNABORTED,e,w)),w=null},i===void 0&&a.setContentType(null),"setRequestHeader"in w&&y.forEach(a.toJSON(),function(k,A){w.setRequestHeader(A,k)}),y.isUndefined(n.withCredentials)||(w.withCredentials=!!n.withCredentials),o&&o!=="json"&&(w.responseType=n.responseType),u&&([v,g]=At(u,!0),w.addEventListener("progress",v)),c&&w.upload&&([m,b]=At(c),w.upload.addEventListener("progress",m),w.upload.addEventListener("loadend",b)),(n.cancelToken||n.signal)&&(f=N=>{w&&(s(!N||N.type?new Ke(null,e,w):N),w.abort(),w=null)},n.cancelToken&&n.cancelToken.subscribe(f),n.signal&&(n.signal.aborted?f():n.signal.addEventListener("abort",f)));const j=Rl(n.url);if(j&&se.protocols.indexOf(j)===-1){s(new F("Unsupported protocol "+j+":",F.ERR_BAD_REQUEST,e));return}w.send(i||null)})},Ll=(e,t)=>{const{length:r}=e=e?e.filter(Boolean):[];if(t||r){let s=new AbortController,n;const i=function(u){if(!n){n=!0,o();const f=u instanceof Error?u:this.reason;s.abort(f instanceof F?f:new Ke(f instanceof Error?f.message:f))}};let a=t&&setTimeout(()=>{a=null,i(new F(`timeout ${t} of ms exceeded`,F.ETIMEDOUT))},t);const o=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(i):u.removeEventListener("abort",i)}),e=null)};e.forEach(u=>u.addEventListener("abort",i));const{signal:c}=s;return c.unsubscribe=()=>y.asap(o),c}},Ul=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let s=0,n;for(;s<r;)n=s+t,yield e.slice(s,n),s=n},Ml=async function*(e,t){for await(const r of ql(e))yield*Ul(r,t)},ql=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:r,value:s}=await t.read();if(r)break;yield s}}finally{await t.cancel()}},os=(e,t,r,s)=>{const n=Ml(e,t);let i=0,a,o=c=>{a||(a=!0,s&&s(c))};return new ReadableStream({async pull(c){try{const{done:u,value:f}=await n.next();if(u){o(),c.close();return}let m=f.byteLength;if(r){let v=i+=m;r(v)}c.enqueue(new Uint8Array(f))}catch(u){throw o(u),u}},cancel(c){return o(c),n.return()}},{highWaterMark:2})},Vt=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",xn=Vt&&typeof ReadableStream=="function",Il=Vt&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),bn=(e,...t)=>{try{return!!e(...t)}catch{return!1}},$l=xn&&bn(()=>{let e=!1;const t=new Request(se.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),ls=64*1024,yr=xn&&bn(()=>y.isReadableStream(new Response("").body)),Ft={stream:yr&&(e=>e.body)};Vt&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Ft[t]&&(Ft[t]=y.isFunction(e[t])?r=>r[t]():(r,s)=>{throw new F(`Response type '${t}' is not supported`,F.ERR_NOT_SUPPORT,s)})})})(new Response);const Ql=async e=>{if(e==null)return 0;if(y.isBlob(e))return e.size;if(y.isSpecCompliantForm(e))return(await new Request(se.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(y.isArrayBufferView(e)||y.isArrayBuffer(e))return e.byteLength;if(y.isURLSearchParams(e)&&(e=e+""),y.isString(e))return(await Il(e)).byteLength},Vl=async(e,t)=>{const r=y.toFiniteNumber(e.getContentLength());return r??Ql(t)},Bl=Vt&&(async e=>{let{url:t,method:r,data:s,signal:n,cancelToken:i,timeout:a,onDownloadProgress:o,onUploadProgress:c,responseType:u,headers:f,withCredentials:m="same-origin",fetchOptions:v}=gn(e);u=u?(u+"").toLowerCase():"text";let b=Ll([n,i&&i.toAbortSignal()],a),g;const h=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let w;try{if(c&&$l&&r!=="get"&&r!=="head"&&(w=await Vl(f,s))!==0){let A=new Request(t,{method:"POST",body:s,duplex:"half"}),U;if(y.isFormData(s)&&(U=A.headers.get("content-type"))&&f.setContentType(U),A.body){const[H,fe]=ns(w,At(is(c)));s=os(A.body,ls,H,fe)}}y.isString(m)||(m=m?"include":"omit");const O="credentials"in Request.prototype;g=new Request(t,{...v,signal:b,method:r.toUpperCase(),headers:f.normalize().toJSON(),body:s,duplex:"half",credentials:O?m:void 0});let j=await fetch(g);const N=yr&&(u==="stream"||u==="response");if(yr&&(o||N&&h)){const A={};["status","statusText","headers"].forEach(G=>{A[G]=j[G]});const U=y.toFiniteNumber(j.headers.get("content-length")),[H,fe]=o&&ns(U,At(is(o),!0))||[];j=new Response(os(j.body,ls,H,()=>{fe&&fe(),h&&h()}),A)}u=u||"text";let k=await Ft[y.findKey(Ft,u)||"text"](j,e);return!N&&h&&h(),await new Promise((A,U)=>{yn(A,U,{data:k,headers:de.from(j.headers),status:j.status,statusText:j.statusText,config:e,request:g})})}catch(O){throw h&&h(),O&&O.name==="TypeError"&&/Load failed|fetch/i.test(O.message)?Object.assign(new F("Network Error",F.ERR_NETWORK,e,g),{cause:O.cause||O}):F.from(O,O&&O.code,e,g)}}),vr={http:il,xhr:kl,fetch:Bl};y.forEach(vr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const us=e=>`- ${e}`,Hl=e=>y.isFunction(e)||e===null||e===!1,wn={getAdapter:e=>{e=y.isArray(e)?e:[e];const{length:t}=e;let r,s;const n={};for(let i=0;i<t;i++){r=e[i];let a;if(s=r,!Hl(r)&&(s=vr[(a=String(r)).toLowerCase()],s===void 0))throw new F(`Unknown adapter '${a}'`);if(s)break;n[a||"#"+i]=s}if(!s){const i=Object.entries(n).map(([o,c])=>`adapter ${o} `+(c===!1?"is not supported by the environment":"is not available in the build"));let a=t?i.length>1?`since :
`+i.map(us).join(`
`):" "+us(i[0]):"as no adapter specified";throw new F("There is no suitable adapter to dispatch the request "+a,"ERR_NOT_SUPPORT")}return s},adapters:vr};function tr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ke(null,e)}function cs(e){return tr(e),e.headers=de.from(e.headers),e.data=er.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),wn.getAdapter(e.adapter||ct.adapter)(e).then(function(s){return tr(e),s.data=er.call(e,e.transformResponse,s),s.headers=de.from(s.headers),s},function(s){return pn(s)||(tr(e),s&&s.response&&(s.response.data=er.call(e,e.transformResponse,s.response),s.response.headers=de.from(s.response.headers))),Promise.reject(s)})}const jn="1.9.0",Bt={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{Bt[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const ds={};Bt.transitional=function(t,r,s){function n(i,a){return"[Axios v"+jn+"] Transitional option '"+i+"'"+a+(s?". "+s:"")}return(i,a,o)=>{if(t===!1)throw new F(n(a," has been removed"+(r?" in "+r:"")),F.ERR_DEPRECATED);return r&&!ds[a]&&(ds[a]=!0,console.warn(n(a," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(i,a,o):!0}};Bt.spelling=function(t){return(r,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function zl(e,t,r){if(typeof e!="object")throw new F("options must be an object",F.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let n=s.length;for(;n-- >0;){const i=s[n],a=t[i];if(a){const o=e[i],c=o===void 0||a(o,i,e);if(c!==!0)throw new F("option "+i+" must be "+c,F.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new F("Unknown option "+i,F.ERR_BAD_OPTION)}}const bt={assertOptions:zl,validators:Bt},xe=bt.validators;let Ue=class{constructor(t){this.defaults=t||{},this.interceptors={request:new rs,response:new rs}}async request(t,r){try{return await this._request(t,r)}catch(s){if(s instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const i=n.stack?n.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=qe(this.defaults,r);const{transitional:s,paramsSerializer:n,headers:i}=r;s!==void 0&&bt.assertOptions(s,{silentJSONParsing:xe.transitional(xe.boolean),forcedJSONParsing:xe.transitional(xe.boolean),clarifyTimeoutError:xe.transitional(xe.boolean)},!1),n!=null&&(y.isFunction(n)?r.paramsSerializer={serialize:n}:bt.assertOptions(n,{encode:xe.function,serialize:xe.function},!0)),r.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?r.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:r.allowAbsoluteUrls=!0),bt.assertOptions(r,{baseUrl:xe.spelling("baseURL"),withXsrfToken:xe.spelling("withXSRFToken")},!0),r.method=(r.method||this.defaults.method||"get").toLowerCase();let a=i&&y.merge(i.common,i[r.method]);i&&y.forEach(["delete","get","head","post","put","patch","common"],g=>{delete i[g]}),r.headers=de.concat(a,i);const o=[];let c=!0;this.interceptors.request.forEach(function(h){typeof h.runWhen=="function"&&h.runWhen(r)===!1||(c=c&&h.synchronous,o.unshift(h.fulfilled,h.rejected))});const u=[];this.interceptors.response.forEach(function(h){u.push(h.fulfilled,h.rejected)});let f,m=0,v;if(!c){const g=[cs.bind(this),void 0];for(g.unshift.apply(g,o),g.push.apply(g,u),v=g.length,f=Promise.resolve(r);m<v;)f=f.then(g[m++],g[m++]);return f}v=o.length;let b=r;for(m=0;m<v;){const g=o[m++],h=o[m++];try{b=g(b)}catch(w){h.call(this,w);break}}try{f=cs.call(this,b)}catch(g){return Promise.reject(g)}for(m=0,v=u.length;m<v;)f=f.then(u[m++],u[m++]);return f}getUri(t){t=qe(this.defaults,t);const r=vn(t.baseURL,t.url,t.allowAbsoluteUrls);return fn(r,t.params,t.paramsSerializer)}};y.forEach(["delete","get","head","options"],function(t){Ue.prototype[t]=function(r,s){return this.request(qe(s||{},{method:t,url:r,data:(s||{}).data}))}});y.forEach(["post","put","patch"],function(t){function r(s){return function(i,a,o){return this.request(qe(o||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:a}))}}Ue.prototype[t]=r(),Ue.prototype[t+"Form"]=r(!0)});let Kl=class Sn{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(i){r=i});const s=this;this.promise.then(n=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](n);s._listeners=null}),this.promise.then=n=>{let i;const a=new Promise(o=>{s.subscribe(o),i=o}).then(n);return a.cancel=function(){s.unsubscribe(i)},a},t(function(i,a,o){s.reason||(s.reason=new Ke(i,a,o),r(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}toAbortSignal(){const t=new AbortController,r=s=>{t.abort(s)};return this.subscribe(r),t.signal.unsubscribe=()=>this.unsubscribe(r),t.signal}static source(){let t;return{token:new Sn(function(n){t=n}),cancel:t}}};function Wl(e){return function(r){return e.apply(null,r)}}function Jl(e){return y.isObject(e)&&e.isAxiosError===!0}const gr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(gr).forEach(([e,t])=>{gr[t]=e});function Nn(e){const t=new Ue(e),r=Ys(Ue.prototype.request,t);return y.extend(r,Ue.prototype,t,{allOwnKeys:!0}),y.extend(r,t,null,{allOwnKeys:!0}),r.create=function(n){return Nn(qe(e,n))},r}const W=Nn(ct);W.Axios=Ue;W.CanceledError=Ke;W.CancelToken=Kl;W.isCancel=pn;W.VERSION=jn;W.toFormData=Qt;W.AxiosError=F;W.Cancel=W.CanceledError;W.all=function(t){return Promise.all(t)};W.spread=Wl;W.isAxiosError=Jl;W.mergeConfig=qe;W.AxiosHeaders=de;W.formToJSON=e=>mn(y.isHTMLForm(e)?new FormData(e):e);W.getAdapter=wn.getAdapter;W.HttpStatusCode=gr;W.default=W;const{Axios:ku,AxiosError:Lu,CanceledError:Uu,isCancel:Mu,CancelToken:qu,VERSION:Iu,all:$u,Cancel:Qu,isAxiosError:Vu,spread:Bu,toFormData:Hu,AxiosHeaders:zu,HttpStatusCode:Ku,formToJSON:Wu,getAdapter:Ju,mergeConfig:Gu}=W,Ee=W.create({baseURL:"http://localhost:3000/api",timeout:1e4,headers:{"Content-Type":"application/json"}});Ee.interceptors.request.use(e=>{const{tokens:t}=Re.getState();return t!=null&&t.accessToken&&(e.headers.Authorization=`Bearer ${t.accessToken}`),e.metadata={startTime:new Date},e},e=>Promise.reject(e));Ee.interceptors.response.use(e=>{var s;const t=new Date,r=(s=e.config.metadata)==null?void 0:s.startTime;if(r){const n=t.getTime()-r.getTime();console.log(`API请求耗时: ${n}ms - ${e.config.url}`)}return e},async e=>{const{response:t}=e;if(!t)return pe.error("网络连接失败，请检查网络设置"),Promise.reject(e);const{status:r,data:s}=t;if(r===401){const{logout:n}=Re.getState();return n(),window.location.pathname.includes("/login")||(pe.error("登录已过期，请重新登录"),window.location.href="/login"),Promise.reject(e)}return r===403?(pe.error((s==null?void 0:s.error)||"没有权限访问该资源"),Promise.reject(e)):r===404?(pe.error((s==null?void 0:s.error)||"请求的资源不存在"),Promise.reject(e)):r===429?(pe.error((s==null?void 0:s.error)||"请求过于频繁，请稍后再试"),Promise.reject(e)):r>=500?(pe.error("服务器内部错误，请稍后再试"),Promise.reject(e)):(s!=null&&s.error&&pe.error(s.error),Promise.reject(e))});class X{static async get(t,r){return(await Ee.get(t,r)).data}static async post(t,r,s){return(await Ee.post(t,r,s)).data}static async put(t,r,s){return(await Ee.put(t,r,s)).data}static async patch(t,r,s){return(await Ee.patch(t,r,s)).data}static async delete(t,r){return(await Ee.delete(t,r)).data}static async upload(t,r,s){const n=new FormData;return n.append("file",r),(await Ee.post(t,n,{headers:{"Content-Type":"multipart/form-data"},onUploadProgress:a=>{if(s&&a.total){const o=Math.round(a.loaded*100/a.total);s(o)}}})).data}static async download(t,r,s){const n=await Ee.get(t,{responseType:"blob",onDownloadProgress:c=>{if(s&&c.total){const u=Math.round(c.loaded*100/c.total);s(u)}}}),i=new Blob([n.data]),a=window.URL.createObjectURL(i),o=document.createElement("a");o.href=a,o.download=r||"download",document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(a)}}class at{static async register(t){return X.post("/auth/register",t)}static async login(t){return X.post("/auth/login",t)}static async getCurrentUser(){return X.get("/auth/me")}static async updateProfile(t){return X.put("/auth/profile",t)}static async requestPasswordReset(t){return X.post("/auth/password/reset-request",t)}static async resetPassword(t){return X.post("/auth/password/reset",t)}static async verifyEmail(t){return X.get(`/auth/verify-email/${t}`)}static async bindSocialAccount(t){return X.post("/auth/social/bind",t)}static async unbindSocialAccount(t){return X.delete(`/auth/social/unbind/${t}`)}static async refreshToken(t){return X.post("/auth/refresh",{refreshToken:t})}static async logout(){return X.post("/auth/logout")}static async checkUsernameAvailability(t){return X.get(`/auth/check-username/${t}`)}static async checkEmailAvailability(t){return X.get(`/auth/check-email/${t}`)}static async resendVerificationEmail(){return X.post("/auth/resend-verification")}static async changePassword(t){return X.post("/auth/change-password",t)}static async deleteAccount(t){return X.delete("/auth/account",{data:{password:t}})}static async getLoginHistory(t=1,r=20){return X.get(`/auth/login-history?page=${t}&limit=${r}`)}static async toggleTwoFactorAuth(t){return X.post("/auth/2fa/toggle",{enabled:t})}static async verifyTwoFactorCode(t){return X.post("/auth/2fa/verify",{code:t})}}var dt=e=>e.type==="checkbox",Le=e=>e instanceof Date,ie=e=>e==null;const En=e=>typeof e=="object";var K=e=>!ie(e)&&!Array.isArray(e)&&En(e)&&!Le(e),Gl=e=>K(e)&&e.target?dt(e.target)?e.target.checked:e.target.value:e,Zl=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,Xl=(e,t)=>e.has(Zl(t)),Yl=e=>{const t=e.constructor&&e.constructor.prototype;return K(t)&&t.hasOwnProperty("isPrototypeOf")},Or=typeof window<"u"&&typeof window.HTMLElement<"u"&&typeof document<"u";function re(e){let t;const r=Array.isArray(e),s=typeof FileList<"u"?e instanceof FileList:!1;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else if(!(Or&&(e instanceof Blob||s))&&(r||K(e)))if(t=r?[]:{},!r&&!Yl(e))t=e;else for(const n in e)e.hasOwnProperty(n)&&(t[n]=re(e[n]));else return e;return t}var Ht=e=>Array.isArray(e)?e.filter(Boolean):[],J=e=>e===void 0,_=(e,t,r)=>{if(!t||!K(e))return r;const s=Ht(t.split(/[,[\].]+?/)).reduce((n,i)=>ie(n)?n:n[i],e);return J(s)||s===e?J(e[t])?r:e[t]:s},be=e=>typeof e=="boolean",Rr=e=>/^\w*$/.test(e),On=e=>Ht(e.replace(/["|']|\]/g,"").split(/\.|\[/)),V=(e,t,r)=>{let s=-1;const n=Rr(t)?[t]:On(t),i=n.length,a=i-1;for(;++s<i;){const o=n[s];let c=r;if(s!==a){const u=e[o];c=K(u)||Array.isArray(u)?u:isNaN(+n[s+1])?{}:[]}if(o==="__proto__"||o==="constructor"||o==="prototype")return;e[o]=c,e=e[o]}};const fs={BLUR:"blur",FOCUS_OUT:"focusout"},ye={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},Ne={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};M.createContext(null);var eu=(e,t,r,s=!0)=>{const n={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(n,i,{get:()=>{const a=i;return t._proxyFormState[a]!==ye.all&&(t._proxyFormState[a]=!s||ye.all),e[a]}});return n};const tu=typeof window<"u"?T.useLayoutEffect:T.useEffect;var we=e=>typeof e=="string",ru=(e,t,r,s,n)=>we(e)?(s&&t.watch.add(e),_(r,e,n)):Array.isArray(e)?e.map(i=>(s&&t.watch.add(i),_(r,i))):(s&&(t.watchAll=!0),r),su=(e,t,r,s,n)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:n||!0}}:{},tt=e=>Array.isArray(e)?e:[e],hs=()=>{let e=[];return{get observers(){return e},next:n=>{for(const i of e)i.next&&i.next(n)},subscribe:n=>(e.push(n),{unsubscribe:()=>{e=e.filter(i=>i!==n)}}),unsubscribe:()=>{e=[]}}},xr=e=>ie(e)||!En(e);function Fe(e,t){if(xr(e)||xr(t))return e===t;if(Le(e)&&Le(t))return e.getTime()===t.getTime();const r=Object.keys(e),s=Object.keys(t);if(r.length!==s.length)return!1;for(const n of r){const i=e[n];if(!s.includes(n))return!1;if(n!=="ref"){const a=t[n];if(Le(i)&&Le(a)||K(i)&&K(a)||Array.isArray(i)&&Array.isArray(a)?!Fe(i,a):i!==a)return!1}}return!0}var ue=e=>K(e)&&!Object.keys(e).length,_r=e=>e.type==="file",ve=e=>typeof e=="function",Pt=e=>{if(!Or)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},Rn=e=>e.type==="select-multiple",Cr=e=>e.type==="radio",nu=e=>Cr(e)||dt(e),rr=e=>Pt(e)&&e.isConnected;function iu(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=J(e)?s++:e[t[s++]];return e}function au(e){for(const t in e)if(e.hasOwnProperty(t)&&!J(e[t]))return!1;return!0}function Y(e,t){const r=Array.isArray(t)?t:Rr(t)?[t]:On(t),s=r.length===1?e:iu(e,r),n=r.length-1,i=r[n];return s&&delete s[i],n!==0&&(K(s)&&ue(s)||Array.isArray(s)&&au(s))&&Y(e,r.slice(0,-1)),e}var _n=e=>{for(const t in e)if(ve(e[t]))return!0;return!1};function Tt(e,t={}){const r=Array.isArray(e);if(K(e)||r)for(const s in e)Array.isArray(e[s])||K(e[s])&&!_n(e[s])?(t[s]=Array.isArray(e[s])?[]:{},Tt(e[s],t[s])):ie(e[s])||(t[s]=!0);return t}function Cn(e,t,r){const s=Array.isArray(e);if(K(e)||s)for(const n in e)Array.isArray(e[n])||K(e[n])&&!_n(e[n])?J(t)||xr(r[n])?r[n]=Array.isArray(e[n])?Tt(e[n],[]):{...Tt(e[n])}:Cn(e[n],ie(t)?{}:t[n],r[n]):r[n]=!Fe(e[n],t[n]);return r}var Ge=(e,t)=>Cn(e,t,Tt(t));const ms={value:!1,isValid:!1},ps={value:!0,isValid:!0};var An=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(r=>r&&r.checked&&!r.disabled).map(r=>r.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!J(e[0].attributes.value)?J(e[0].value)||e[0].value===""?ps:{value:e[0].value,isValid:!0}:ps:ms}return ms},Fn=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>J(e)?e:t?e===""?NaN:e&&+e:r&&we(e)?new Date(e):s?s(e):e;const ys={isValid:!1,value:null};var Pn=e=>Array.isArray(e)?e.reduce((t,r)=>r&&r.checked&&!r.disabled?{isValid:!0,value:r.value}:t,ys):ys;function vs(e){const t=e.ref;return _r(t)?t.files:Cr(t)?Pn(e.refs).value:Rn(t)?[...t.selectedOptions].map(({value:r})=>r):dt(t)?An(e.refs).value:Fn(J(t.value)?e.ref.value:t.value,e)}var ou=(e,t,r,s)=>{const n={};for(const i of e){const a=_(t,i);a&&V(n,i,a._f)}return{criteriaMode:r,names:[...e],fields:n,shouldUseNativeValidation:s}},Dt=e=>e instanceof RegExp,Ze=e=>J(e)?e:Dt(e)?e.source:K(e)?Dt(e.value)?e.value.source:e.value:e,gs=e=>({isOnSubmit:!e||e===ye.onSubmit,isOnBlur:e===ye.onBlur,isOnChange:e===ye.onChange,isOnAll:e===ye.all,isOnTouch:e===ye.onTouched});const xs="AsyncFunction";var lu=e=>!!e&&!!e.validate&&!!(ve(e.validate)&&e.validate.constructor.name===xs||K(e.validate)&&Object.values(e.validate).find(t=>t.constructor.name===xs)),uu=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),bs=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some(s=>e.startsWith(s)&&/^\.\w+/.test(e.slice(s.length))));const rt=(e,t,r,s)=>{for(const n of r||Object.keys(e)){const i=_(e,n);if(i){const{_f:a,...o}=i;if(a){if(a.refs&&a.refs[0]&&t(a.refs[0],n)&&!s)return!0;if(a.ref&&t(a.ref,a.name)&&!s)return!0;if(rt(o,t))break}else if(K(o)&&rt(o,t))break}}};function ws(e,t,r){const s=_(e,r);if(s||Rr(r))return{error:s,name:r};const n=r.split(".");for(;n.length;){const i=n.join("."),a=_(t,i),o=_(e,i);if(a&&!Array.isArray(a)&&r!==i)return{name:r};if(o&&o.type)return{name:i,error:o};if(o&&o.root&&o.root.type)return{name:`${i}.root`,error:o.root};n.pop()}return{name:r}}var cu=(e,t,r,s)=>{r(e);const{name:n,...i}=e;return ue(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find(a=>t[a]===(!s||ye.all))},du=(e,t,r)=>!e||!t||e===t||tt(e).some(s=>s&&(r?s===t:s.startsWith(t)||t.startsWith(s))),fu=(e,t,r,s,n)=>n.isOnAll?!1:!r&&n.isOnTouch?!(t||e):(r?s.isOnBlur:n.isOnBlur)?!e:(r?s.isOnChange:n.isOnChange)?e:!0,hu=(e,t)=>!Ht(_(e,t)).length&&Y(e,t),mu=(e,t,r)=>{const s=tt(_(e,r));return V(s,"root",t[r]),V(e,r,s),e},wt=e=>we(e);function js(e,t,r="validate"){if(wt(e)||Array.isArray(e)&&e.every(wt)||be(e)&&!e)return{type:r,message:wt(e)?e:"",ref:t}}var Qe=e=>K(e)&&!Dt(e)?e:{value:e,message:""},Ss=async(e,t,r,s,n,i)=>{const{ref:a,refs:o,required:c,maxLength:u,minLength:f,min:m,max:v,pattern:b,validate:g,name:h,valueAsNumber:w,mount:O}=e._f,j=_(r,h);if(!O||t.has(h))return{};const N=o?o[0]:a,k=P=>{n&&N.reportValidity&&(N.setCustomValidity(be(P)?"":P||""),N.reportValidity())},A={},U=Cr(a),H=dt(a),fe=U||H,G=(w||_r(a))&&J(a.value)&&J(j)||Pt(a)&&a.value===""||j===""||Array.isArray(j)&&!j.length,Z=su.bind(null,h,s,A),ne=(P,L,q,z=Ne.maxLength,Q=Ne.minLength)=>{const te=P?L:q;A[h]={type:P?z:Q,message:te,ref:a,...Z(P?z:Q,te)}};if(i?!Array.isArray(j)||!j.length:c&&(!fe&&(G||ie(j))||be(j)&&!j||H&&!An(o).isValid||U&&!Pn(o).isValid)){const{value:P,message:L}=wt(c)?{value:!!c,message:c}:Qe(c);if(P&&(A[h]={type:Ne.required,message:L,ref:N,...Z(Ne.required,L)},!s))return k(L),A}if(!G&&(!ie(m)||!ie(v))){let P,L;const q=Qe(v),z=Qe(m);if(!ie(j)&&!isNaN(j)){const Q=a.valueAsNumber||j&&+j;ie(q.value)||(P=Q>q.value),ie(z.value)||(L=Q<z.value)}else{const Q=a.valueAsDate||new Date(j),te=je=>new Date(new Date().toDateString()+" "+je),oe=a.type=="time",le=a.type=="week";we(q.value)&&j&&(P=oe?te(j)>te(q.value):le?j>q.value:Q>new Date(q.value)),we(z.value)&&j&&(L=oe?te(j)<te(z.value):le?j<z.value:Q<new Date(z.value))}if((P||L)&&(ne(!!P,q.message,z.message,Ne.max,Ne.min),!s))return k(A[h].message),A}if((u||f)&&!G&&(we(j)||i&&Array.isArray(j))){const P=Qe(u),L=Qe(f),q=!ie(P.value)&&j.length>+P.value,z=!ie(L.value)&&j.length<+L.value;if((q||z)&&(ne(q,P.message,L.message),!s))return k(A[h].message),A}if(b&&!G&&we(j)){const{value:P,message:L}=Qe(b);if(Dt(P)&&!j.match(P)&&(A[h]={type:Ne.pattern,message:L,ref:a,...Z(Ne.pattern,L)},!s))return k(L),A}if(g){if(ve(g)){const P=await g(j,r),L=js(P,N);if(L&&(A[h]={...L,...Z(Ne.validate,L.message)},!s))return k(L.message),A}else if(K(g)){let P={};for(const L in g){if(!ue(P)&&!s)break;const q=js(await g[L](j,r),N,L);q&&(P={...q,...Z(L,q.message)},k(q.message),s&&(A[h]=P))}if(!ue(P)&&(A[h]={ref:N,...P},!s))return A}}return k(!0),A};const pu={mode:ye.onSubmit,reValidateMode:ye.onChange,shouldFocusError:!0};function yu(e={}){let t={...pu,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:ve(t.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1};const s={};let n=K(t.defaultValues)||K(t.values)?re(t.defaultValues||t.values)||{}:{},i=t.shouldUnregister?{}:re(n),a={action:!1,mount:!1,watch:!1},o={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c,u=0;const f={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let m={...f};const v={array:hs(),state:hs()},b=t.criteriaMode===ye.all,g=d=>p=>{clearTimeout(u),u=setTimeout(d,p)},h=async d=>{if(!t.disabled&&(f.isValid||m.isValid||d)){const p=t.resolver?ue((await H()).errors):await G(s,!0);p!==r.isValid&&v.state.next({isValid:p})}},w=(d,p)=>{!t.disabled&&(f.isValidating||f.validatingFields||m.isValidating||m.validatingFields)&&((d||Array.from(o.mount)).forEach(x=>{x&&(p?V(r.validatingFields,x,p):Y(r.validatingFields,x))}),v.state.next({validatingFields:r.validatingFields,isValidating:!ue(r.validatingFields)}))},O=(d,p=[],x,R,E=!0,S=!0)=>{if(R&&x&&!t.disabled){if(a.action=!0,S&&Array.isArray(_(s,d))){const C=x(_(s,d),R.argA,R.argB);E&&V(s,d,C)}if(S&&Array.isArray(_(r.errors,d))){const C=x(_(r.errors,d),R.argA,R.argB);E&&V(r.errors,d,C),hu(r.errors,d)}if((f.touchedFields||m.touchedFields)&&S&&Array.isArray(_(r.touchedFields,d))){const C=x(_(r.touchedFields,d),R.argA,R.argB);E&&V(r.touchedFields,d,C)}(f.dirtyFields||m.dirtyFields)&&(r.dirtyFields=Ge(n,i)),v.state.next({name:d,isDirty:ne(d,p),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else V(i,d,p)},j=(d,p)=>{V(r.errors,d,p),v.state.next({errors:r.errors})},N=d=>{r.errors=d,v.state.next({errors:r.errors,isValid:!1})},k=(d,p,x,R)=>{const E=_(s,d);if(E){const S=_(i,d,J(x)?_(n,d):x);J(S)||R&&R.defaultChecked||p?V(i,d,p?S:vs(E._f)):q(d,S),a.mount&&h()}},A=(d,p,x,R,E)=>{let S=!1,C=!1;const I={name:d};if(!t.disabled){if(!x||R){(f.isDirty||m.isDirty)&&(C=r.isDirty,r.isDirty=I.isDirty=ne(),S=C!==I.isDirty);const $=Fe(_(n,d),p);C=!!_(r.dirtyFields,d),$?Y(r.dirtyFields,d):V(r.dirtyFields,d,!0),I.dirtyFields=r.dirtyFields,S=S||(f.dirtyFields||m.dirtyFields)&&C!==!$}if(x){const $=_(r.touchedFields,d);$||(V(r.touchedFields,d,x),I.touchedFields=r.touchedFields,S=S||(f.touchedFields||m.touchedFields)&&$!==x)}S&&E&&v.state.next(I)}return S?I:{}},U=(d,p,x,R)=>{const E=_(r.errors,d),S=(f.isValid||m.isValid)&&be(p)&&r.isValid!==p;if(t.delayError&&x?(c=g(()=>j(d,x)),c(t.delayError)):(clearTimeout(u),c=null,x?V(r.errors,d,x):Y(r.errors,d)),(x?!Fe(E,x):E)||!ue(R)||S){const C={...R,...S&&be(p)?{isValid:p}:{},errors:r.errors,name:d};r={...r,...C},v.state.next(C)}},H=async d=>{w(d,!0);const p=await t.resolver(i,t.context,ou(d||o.mount,s,t.criteriaMode,t.shouldUseNativeValidation));return w(d),p},fe=async d=>{const{errors:p}=await H(d);if(d)for(const x of d){const R=_(p,x);R?V(r.errors,x,R):Y(r.errors,x)}else r.errors=p;return p},G=async(d,p,x={valid:!0})=>{for(const R in d){const E=d[R];if(E){const{_f:S,...C}=E;if(S){const I=o.array.has(S.name),$=E._f&&lu(E._f);$&&f.validatingFields&&w([R],!0);const he=await Ss(E,o.disabled,i,b,t.shouldUseNativeValidation&&!p,I);if($&&f.validatingFields&&w([R]),he[S.name]&&(x.valid=!1,p))break;!p&&(_(he,S.name)?I?mu(r.errors,he,S.name):V(r.errors,S.name,he[S.name]):Y(r.errors,S.name))}!ue(C)&&await G(C,p,x)}}return x.valid},Z=()=>{for(const d of o.unMount){const p=_(s,d);p&&(p._f.refs?p._f.refs.every(x=>!rr(x)):!rr(p._f.ref))&&Kt(d)}o.unMount=new Set},ne=(d,p)=>!t.disabled&&(d&&p&&V(i,d,p),!Fe(je(),n)),P=(d,p,x)=>ru(d,o,{...a.mount?i:J(p)?n:we(d)?{[d]:p}:p},x,p),L=d=>Ht(_(a.mount?i:n,d,t.shouldUnregister?_(n,d,[]):[])),q=(d,p,x={})=>{const R=_(s,d);let E=p;if(R){const S=R._f;S&&(!S.disabled&&V(i,d,Fn(p,S)),E=Pt(S.ref)&&ie(p)?"":p,Rn(S.ref)?[...S.ref.options].forEach(C=>C.selected=E.includes(C.value)):S.refs?dt(S.ref)?S.refs.forEach(C=>{(!C.defaultChecked||!C.disabled)&&(Array.isArray(E)?C.checked=!!E.find(I=>I===C.value):C.checked=E===C.value||!!E)}):S.refs.forEach(C=>C.checked=C.value===E):_r(S.ref)?S.ref.value="":(S.ref.value=E,S.ref.type||v.state.next({name:d,values:re(i)})))}(x.shouldDirty||x.shouldTouch)&&A(d,E,x.shouldTouch,x.shouldDirty,!0),x.shouldValidate&&le(d)},z=(d,p,x)=>{for(const R in p){if(!p.hasOwnProperty(R))return;const E=p[R],S=d+"."+R,C=_(s,S);(o.array.has(d)||K(E)||C&&!C._f)&&!Le(E)?z(S,E,x):q(S,E,x)}},Q=(d,p,x={})=>{const R=_(s,d),E=o.array.has(d),S=re(p);V(i,d,S),E?(v.array.next({name:d,values:re(i)}),(f.isDirty||f.dirtyFields||m.isDirty||m.dirtyFields)&&x.shouldDirty&&v.state.next({name:d,dirtyFields:Ge(n,i),isDirty:ne(d,S)})):R&&!R._f&&!ie(S)?z(d,S,x):q(d,S,x),bs(d,o)&&v.state.next({...r}),v.state.next({name:a.mount?d:void 0,values:re(i)})},te=async d=>{a.mount=!0;const p=d.target;let x=p.name,R=!0;const E=_(s,x),S=$=>{R=Number.isNaN($)||Le($)&&isNaN($.getTime())||Fe($,_(i,x,$))},C=gs(t.mode),I=gs(t.reValidateMode);if(E){let $,he;const ft=p.type?vs(E._f):Gl(d),_e=d.type===fs.BLUR||d.type===fs.FOCUS_OUT,$n=!uu(E._f)&&!t.resolver&&!_(r.errors,x)&&!E._f.deps||fu(_e,_(r.touchedFields,x),r.isSubmitted,I,C),Gt=bs(x,o,_e);V(i,x,ft),_e?(E._f.onBlur&&E._f.onBlur(d),c&&c(0)):E._f.onChange&&E._f.onChange(d);const Zt=A(x,ft,_e),Qn=!ue(Zt)||Gt;if(!_e&&v.state.next({name:x,type:d.type,values:re(i)}),$n)return(f.isValid||m.isValid)&&(t.mode==="onBlur"?_e&&h():_e||h()),Qn&&v.state.next({name:x,...Gt?{}:Zt});if(!_e&&Gt&&v.state.next({...r}),t.resolver){const{errors:Ur}=await H([x]);if(S(ft),R){const Vn=ws(r.errors,s,x),Mr=ws(Ur,s,Vn.name||x);$=Mr.error,x=Mr.name,he=ue(Ur)}}else w([x],!0),$=(await Ss(E,o.disabled,i,b,t.shouldUseNativeValidation))[x],w([x]),S(ft),R&&($?he=!1:(f.isValid||m.isValid)&&(he=await G(s,!0)));R&&(E._f.deps&&le(E._f.deps),U(x,he,$,Zt))}},oe=(d,p)=>{if(_(r.errors,p)&&d.focus)return d.focus(),1},le=async(d,p={})=>{let x,R;const E=tt(d);if(t.resolver){const S=await fe(J(d)?d:E);x=ue(S),R=d?!E.some(C=>_(S,C)):x}else d?(R=(await Promise.all(E.map(async S=>{const C=_(s,S);return await G(C&&C._f?{[S]:C}:C)}))).every(Boolean),!(!R&&!r.isValid)&&h()):R=x=await G(s);return v.state.next({...!we(d)||(f.isValid||m.isValid)&&x!==r.isValid?{}:{name:d},...t.resolver||!d?{isValid:x}:{},errors:r.errors}),p.shouldFocus&&!R&&rt(s,oe,d?E:o.mount),R},je=d=>{const p={...a.mount?i:n};return J(d)?p:we(d)?_(p,d):d.map(x=>_(p,x))},$e=(d,p)=>({invalid:!!_((p||r).errors,d),isDirty:!!_((p||r).dirtyFields,d),error:_((p||r).errors,d),isValidating:!!_(r.validatingFields,d),isTouched:!!_((p||r).touchedFields,d)}),zt=d=>{d&&tt(d).forEach(p=>Y(r.errors,p)),v.state.next({errors:d?r.errors:{}})},Ar=(d,p,x)=>{const R=(_(s,d,{_f:{}})._f||{}).ref,E=_(r.errors,d)||{},{ref:S,message:C,type:I,...$}=E;V(r.errors,d,{...$,...p,ref:R}),v.state.next({name:d,errors:r.errors,isValid:!1}),x&&x.shouldFocus&&R&&R.focus&&R.focus()},kn=(d,p)=>ve(d)?v.state.subscribe({next:x=>d(P(void 0,p),x)}):P(d,p,!0),Fr=d=>v.state.subscribe({next:p=>{du(d.name,p.name,d.exact)&&cu(p,d.formState||f,In,d.reRenderRoot)&&d.callback({values:{...i},...r,...p})}}).unsubscribe,Ln=d=>(a.mount=!0,m={...m,...d.formState},Fr({...d,formState:m})),Kt=(d,p={})=>{for(const x of d?tt(d):o.mount)o.mount.delete(x),o.array.delete(x),p.keepValue||(Y(s,x),Y(i,x)),!p.keepError&&Y(r.errors,x),!p.keepDirty&&Y(r.dirtyFields,x),!p.keepTouched&&Y(r.touchedFields,x),!p.keepIsValidating&&Y(r.validatingFields,x),!t.shouldUnregister&&!p.keepDefaultValue&&Y(n,x);v.state.next({values:re(i)}),v.state.next({...r,...p.keepDirty?{isDirty:ne()}:{}}),!p.keepIsValid&&h()},Pr=({disabled:d,name:p})=>{(be(d)&&a.mount||d||o.disabled.has(p))&&(d?o.disabled.add(p):o.disabled.delete(p))},Wt=(d,p={})=>{let x=_(s,d);const R=be(p.disabled)||be(t.disabled);return V(s,d,{...x||{},_f:{...x&&x._f?x._f:{ref:{name:d}},name:d,mount:!0,...p}}),o.mount.add(d),x?Pr({disabled:be(p.disabled)?p.disabled:t.disabled,name:d}):k(d,!0,p.value),{...R?{disabled:p.disabled||t.disabled}:{},...t.progressive?{required:!!p.required,min:Ze(p.min),max:Ze(p.max),minLength:Ze(p.minLength),maxLength:Ze(p.maxLength),pattern:Ze(p.pattern)}:{},name:d,onChange:te,onBlur:te,ref:E=>{if(E){Wt(d,p),x=_(s,d);const S=J(E.value)&&E.querySelectorAll&&E.querySelectorAll("input,select,textarea")[0]||E,C=nu(S),I=x._f.refs||[];if(C?I.find($=>$===S):S===x._f.ref)return;V(s,d,{_f:{...x._f,...C?{refs:[...I.filter(rr),S,...Array.isArray(_(n,d))?[{}]:[]],ref:{type:S.type,name:d}}:{ref:S}}}),k(d,!1,void 0,S)}else x=_(s,d,{}),x._f&&(x._f.mount=!1),(t.shouldUnregister||p.shouldUnregister)&&!(Xl(o.array,d)&&a.action)&&o.unMount.add(d)}}},Jt=()=>t.shouldFocusError&&rt(s,oe,o.mount),Un=d=>{be(d)&&(v.state.next({disabled:d}),rt(s,(p,x)=>{const R=_(s,x);R&&(p.disabled=R._f.disabled||d,Array.isArray(R._f.refs)&&R._f.refs.forEach(E=>{E.disabled=R._f.disabled||d}))},0,!1))},Tr=(d,p)=>async x=>{let R;x&&(x.preventDefault&&x.preventDefault(),x.persist&&x.persist());let E=re(i);if(v.state.next({isSubmitting:!0}),t.resolver){const{errors:S,values:C}=await H();r.errors=S,E=C}else await G(s);if(o.disabled.size)for(const S of o.disabled)V(E,S,void 0);if(Y(r.errors,"root"),ue(r.errors)){v.state.next({errors:{}});try{await d(E,x)}catch(S){R=S}}else p&&await p({...r.errors},x),Jt(),setTimeout(Jt);if(v.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:ue(r.errors)&&!R,submitCount:r.submitCount+1,errors:r.errors}),R)throw R},Mn=(d,p={})=>{_(s,d)&&(J(p.defaultValue)?Q(d,re(_(n,d))):(Q(d,p.defaultValue),V(n,d,re(p.defaultValue))),p.keepTouched||Y(r.touchedFields,d),p.keepDirty||(Y(r.dirtyFields,d),r.isDirty=p.defaultValue?ne(d,re(_(n,d))):ne()),p.keepError||(Y(r.errors,d),f.isValid&&h()),v.state.next({...r}))},Dr=(d,p={})=>{const x=d?re(d):n,R=re(x),E=ue(d),S=E?n:R;if(p.keepDefaultValues||(n=x),!p.keepValues){if(p.keepDirtyValues){const C=new Set([...o.mount,...Object.keys(Ge(n,i))]);for(const I of Array.from(C))_(r.dirtyFields,I)?V(S,I,_(i,I)):Q(I,_(S,I))}else{if(Or&&J(d))for(const C of o.mount){const I=_(s,C);if(I&&I._f){const $=Array.isArray(I._f.refs)?I._f.refs[0]:I._f.ref;if(Pt($)){const he=$.closest("form");if(he){he.reset();break}}}}for(const C of o.mount)Q(C,_(S,C))}i=re(S),v.array.next({values:{...S}}),v.state.next({values:{...S}})}o={mount:p.keepDirtyValues?o.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},a.mount=!f.isValid||!!p.keepIsValid||!!p.keepDirtyValues,a.watch=!!t.shouldUnregister,v.state.next({submitCount:p.keepSubmitCount?r.submitCount:0,isDirty:E?!1:p.keepDirty?r.isDirty:!!(p.keepDefaultValues&&!Fe(d,n)),isSubmitted:p.keepIsSubmitted?r.isSubmitted:!1,dirtyFields:E?{}:p.keepDirtyValues?p.keepDefaultValues&&i?Ge(n,i):r.dirtyFields:p.keepDefaultValues&&d?Ge(n,d):p.keepDirty?r.dirtyFields:{},touchedFields:p.keepTouched?r.touchedFields:{},errors:p.keepErrors?r.errors:{},isSubmitSuccessful:p.keepIsSubmitSuccessful?r.isSubmitSuccessful:!1,isSubmitting:!1})},kr=(d,p)=>Dr(ve(d)?d(i):d,p),qn=(d,p={})=>{const x=_(s,d),R=x&&x._f;if(R){const E=R.refs?R.refs[0]:R.ref;E.focus&&(E.focus(),p.shouldSelect&&ve(E.select)&&E.select())}},In=d=>{r={...r,...d}},Lr={control:{register:Wt,unregister:Kt,getFieldState:$e,handleSubmit:Tr,setError:Ar,_subscribe:Fr,_runSchema:H,_focusError:Jt,_getWatch:P,_getDirty:ne,_setValid:h,_setFieldArray:O,_setDisabledField:Pr,_setErrors:N,_getFieldArray:L,_reset:Dr,_resetDefaultValues:()=>ve(t.defaultValues)&&t.defaultValues().then(d=>{kr(d,t.resetOptions),v.state.next({isLoading:!1})}),_removeUnmounted:Z,_disableForm:Un,_subjects:v,_proxyFormState:f,get _fields(){return s},get _formValues(){return i},get _state(){return a},set _state(d){a=d},get _defaultValues(){return n},get _names(){return o},set _names(d){o=d},get _formState(){return r},get _options(){return t},set _options(d){t={...t,...d}}},subscribe:Ln,trigger:le,register:Wt,handleSubmit:Tr,watch:kn,setValue:Q,getValues:je,reset:kr,resetField:Mn,clearErrors:zt,unregister:Kt,setError:Ar,setFocus:qn,getFieldState:$e};return{...Lr,formControl:Lr}}function Tn(e={}){const t=M.useRef(void 0),r=M.useRef(void 0),[s,n]=M.useState({isDirty:!1,isValidating:!1,isLoading:ve(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:ve(e.defaultValues)?void 0:e.defaultValues});t.current||(t.current={...e.formControl?e.formControl:yu(e),formState:s},e.formControl&&e.defaultValues&&!ve(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions));const i=t.current.control;return i._options=e,tu(()=>{const a=i._subscribe({formState:i._proxyFormState,callback:()=>n({...i._formState}),reRenderRoot:!0});return n(o=>({...o,isReady:!0})),i._formState.isReady=!0,a},[i]),M.useEffect(()=>i._disableForm(e.disabled),[i,e.disabled]),M.useEffect(()=>{e.mode&&(i._options.mode=e.mode),e.reValidateMode&&(i._options.reValidateMode=e.reValidateMode)},[i,e.mode,e.reValidateMode]),M.useEffect(()=>{e.errors&&(i._setErrors(e.errors),i._focusError())},[i,e.errors]),M.useEffect(()=>{e.shouldUnregister&&i._subjects.state.next({values:i._getWatch()})},[i,e.shouldUnregister]),M.useEffect(()=>{if(i._proxyFormState.isDirty){const a=i._getDirty();a!==s.isDirty&&i._subjects.state.next({isDirty:a})}},[i,s.isDirty]),M.useEffect(()=>{e.values&&!Fe(e.values,r.current)?(i._reset(e.values,i._options.resetOptions),r.current=e.values,n(a=>({...a}))):i._resetDefaultValues()},[i,e.values]),M.useEffect(()=>{i._state.mount||(i._setValid(),i._state.mount=!0),i._state.watch&&(i._state.watch=!1,i._subjects.state.next({...i._formState})),i._removeUnmounted()}),t.current.formState=eu(s,i),t.current}function Dn(e){var t,r,s="";if(typeof e=="string"||typeof e=="number")s+=e;else if(typeof e=="object")if(Array.isArray(e)){var n=e.length;for(t=0;t<n;t++)e[t]&&(r=Dn(e[t]))&&(s&&(s+=" "),s+=r)}else for(r in e)e[r]&&(s&&(s+=" "),s+=r);return s}function st(){for(var e,t,r=0,s="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=Dn(e))&&(s&&(s+=" "),s+=t);return s}const We=({size:e="md",text:t,className:r})=>{const s={sm:"w-4 h-4",md:"w-8 h-8",lg:"w-12 h-12"},n={sm:"text-sm",md:"text-base",lg:"text-lg"};return l.jsxs("div",{className:st("flex flex-col items-center justify-center",r),children:[l.jsx("div",{className:st("loading-spinner",s[e])}),t&&l.jsx("p",{className:st("mt-2 text-gray-600",n[e]),children:t})]})},vu=()=>{const[e,t]=T.useState(!1),r=Es(),{login:s}=Re(),{register:n,handleSubmit:i,formState:{errors:a}}=Tn(),o=Qs(at.login,{onSuccess:u=>{u.success&&u.data&&(s(u.data.user,u.data.tokens),pe.success("登录成功！"),r("/dashboard"))},onError:u=>{var f,m;pe.error(((m=(f=u.response)==null?void 0:f.data)==null?void 0:m.error)||"登录失败，请重试")}}),c=u=>{o.mutate(u)};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"max-w-md w-full space-y-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"欢迎回来"}),l.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"登录您的账号，开始互助点赞之旅"})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[l.jsxs("form",{className:"space-y-6",onSubmit:i(c),children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Os,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{...n("email",{required:"请输入邮箱地址",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}}),type:"email",className:`input pl-10 ${a.email?"input-error":""}`,placeholder:"请输入邮箱地址"})]}),a.email&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.email.message})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"密码"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(sr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{...n("password",{required:"请输入密码",minLength:{value:6,message:"密码至少6位"}}),type:e?"text":"password",className:`input pl-10 pr-10 ${a.password?"input-error":""}`,placeholder:"请输入密码"}),l.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?l.jsx(nr,{className:"h-5 w-5 text-gray-400"}):l.jsx(ir,{className:"h-5 w-5 text-gray-400"})})]}),a.password&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:a.password.message})]}),l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsx("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"记住我"})]}),l.jsx("div",{className:"text-sm",children:l.jsx(Me,{to:"/forgot-password",className:"font-medium text-primary-600 hover:text-primary-500",children:"忘记密码？"})})]}),l.jsx("button",{type:"submit",disabled:o.isLoading,className:"btn btn-primary w-full flex items-center justify-center",children:o.isLoading?l.jsx(We,{size:"sm"}):"登录"})]}),l.jsx("div",{className:"mt-6",children:l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-0 flex items-center",children:l.jsx("div",{className:"w-full border-t border-gray-300"})}),l.jsx("div",{className:"relative flex justify-center text-sm",children:l.jsx("span",{className:"px-2 bg-white text-gray-500",children:"或者"})})]})}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-sm text-gray-600",children:["还没有账号？"," ",l.jsx(Me,{to:"/register",className:"font-medium text-primary-600 hover:text-primary-500",children:"立即注册"})]})})]}),l.jsx("div",{className:"text-center",children:l.jsxs("p",{className:"text-xs text-gray-500",children:["登录即表示您同意我们的"," ",l.jsx("a",{href:"#",className:"text-primary-600 hover:text-primary-500",children:"服务条款"})," ","和"," ",l.jsx("a",{href:"#",className:"text-primary-600 hover:text-primary-500",children:"隐私政策"})]})})]})})},gu=()=>{const[e,t]=T.useState(!1),[r,s]=T.useState(!1),n=Es(),{login:i}=Re(),{register:a,handleSubmit:o,watch:c,formState:{errors:u}}=Tn(),f=c("password"),m=Qs(at.register,{onSuccess:b=>{b.success&&b.data&&(i(b.data.user,b.data.tokens),pe.success("注册成功！欢迎加入互助点赞平台！"),n("/dashboard"))},onError:b=>{var g,h;pe.error(((h=(g=b.response)==null?void 0:g.data)==null?void 0:h.error)||"注册失败，请重试")}}),v=b=>{const{confirmPassword:g,...h}=b;m.mutate(h)};return l.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4 sm:px-6 lg:px-8",children:l.jsxs("div",{className:"max-w-md w-full space-y-8",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("h2",{className:"mt-6 text-3xl font-extrabold text-gray-900",children:"创建账号"}),l.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"加入互助点赞平台，开始您的积分之旅"})]}),l.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[l.jsxs("form",{className:"space-y-6",onSubmit:o(v),children:[l.jsxs("div",{children:[l.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700 mb-2",children:"用户名"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Xe,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{...a("username",{required:"请输入用户名",minLength:{value:3,message:"用户名至少3位"},maxLength:{value:20,message:"用户名最多20位"},pattern:{value:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线"}}),type:"text",className:`input pl-10 ${u.username?"input-error":""}`,placeholder:"请输入用户名"})]}),u.username&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.username.message})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(Os,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{...a("email",{required:"请输入邮箱地址",pattern:{value:/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,message:"请输入有效的邮箱地址"}}),type:"email",className:`input pl-10 ${u.email?"input-error":""}`,placeholder:"请输入邮箱地址"})]}),u.email&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.email.message})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 mb-2",children:"密码"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(sr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{...a("password",{required:"请输入密码",minLength:{value:6,message:"密码至少6位"},pattern:{value:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,message:"密码必须包含大小写字母和数字"}}),type:e?"text":"password",className:`input pl-10 pr-10 ${u.password?"input-error":""}`,placeholder:"请输入密码"}),l.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>t(!e),children:e?l.jsx(nr,{className:"h-5 w-5 text-gray-400"}):l.jsx(ir,{className:"h-5 w-5 text-gray-400"})})]}),u.password&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.password.message})]}),l.jsxs("div",{children:[l.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700 mb-2",children:"确认密码"}),l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:l.jsx(sr,{className:"h-5 w-5 text-gray-400"})}),l.jsx("input",{...a("confirmPassword",{required:"请确认密码",validate:b=>b===f||"两次输入的密码不一致"}),type:r?"text":"password",className:`input pl-10 pr-10 ${u.confirmPassword?"input-error":""}`,placeholder:"请再次输入密码"}),l.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>s(!r),children:r?l.jsx(nr,{className:"h-5 w-5 text-gray-400"}):l.jsx(ir,{className:"h-5 w-5 text-gray-400"})})]}),u.confirmPassword&&l.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.confirmPassword.message})]}),l.jsxs("div",{className:"flex items-center",children:[l.jsx("input",{id:"agree-terms",name:"agree-terms",type:"checkbox",required:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"}),l.jsxs("label",{htmlFor:"agree-terms",className:"ml-2 block text-sm text-gray-900",children:["我同意"," ",l.jsx("a",{href:"#",className:"text-primary-600 hover:text-primary-500",children:"服务条款"})," ","和"," ",l.jsx("a",{href:"#",className:"text-primary-600 hover:text-primary-500",children:"隐私政策"})]})]}),l.jsx("button",{type:"submit",disabled:m.isLoading,className:"btn btn-primary w-full flex items-center justify-center",children:m.isLoading?l.jsx(We,{size:"sm"}):"创建账号"})]}),l.jsx("div",{className:"mt-6",children:l.jsxs("div",{className:"relative",children:[l.jsx("div",{className:"absolute inset-0 flex items-center",children:l.jsx("div",{className:"w-full border-t border-gray-300"})}),l.jsx("div",{className:"relative flex justify-center text-sm",children:l.jsx("span",{className:"px-2 bg-white text-gray-500",children:"或者"})})]})}),l.jsx("div",{className:"mt-6 text-center",children:l.jsxs("p",{className:"text-sm text-gray-600",children:["已有账号？"," ",l.jsx(Me,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500",children:"立即登录"})]})})]}),l.jsxs("div",{className:"bg-gradient-primary rounded-lg p-4 text-white text-center",children:[l.jsx("p",{className:"text-sm font-medium",children:"🎉 注册即送100积分"}),l.jsx("p",{className:"text-xs mt-1 opacity-90",children:"立即开始您的互助点赞之旅"})]})]})})},xu=()=>{const{user:e}=Re(),{data:t,isLoading:r}=Vs("userStats",at.getCurrentUser,{select:a=>{var o;return(o=a.data)==null?void 0:o.stats}});if(r)return l.jsx("div",{className:"flex items-center justify-center h-64",children:l.jsx(We,{size:"lg",text:"加载中..."})});const s=[{name:"当前积分",value:(e==null?void 0:e.points_balance)||0,icon:jt,color:"text-yellow-600",bgColor:"bg-yellow-100",change:"+12",changeType:"increase"},{name:"发布任务",value:(t==null?void 0:t.totalTasksPublished)||0,icon:Zn,color:"text-blue-600",bgColor:"bg-blue-100",change:"+2",changeType:"increase"},{name:"完成任务",value:(t==null?void 0:t.totalTasksCompleted)||0,icon:Xn,color:"text-green-600",bgColor:"bg-green-100",change:"+5",changeType:"increase"},{name:"总收益",value:(t==null?void 0:t.totalPointsEarned)||0,icon:Yn,color:"text-purple-600",bgColor:"bg-purple-100",change:"+18",changeType:"increase"}],n=[{name:"发布任务",description:"发布新的点赞任务",href:"/tasks/create",icon:Rs,color:"bg-blue-600 hover:bg-blue-700"},{name:"任务大厅",description:"浏览可执行的任务",href:"/tasks",icon:_s,color:"bg-green-600 hover:bg-green-700"},{name:"积分记录",description:"查看积分流水",href:"/points",icon:jt,color:"bg-yellow-600 hover:bg-yellow-700"}],i=[{id:1,type:"task_completed",title:"完成点赞任务",description:"抖音视频点赞任务",points:"+5",time:"2分钟前"},{id:2,type:"task_published",title:"发布新任务",description:"快手视频点赞任务",points:"-20",time:"1小时前"},{id:3,type:"reward",title:"获得奖励",description:"连续签到7天奖励",points:"+10",time:"昨天"}];return l.jsxs("div",{className:"space-y-8",children:[l.jsx("div",{className:"bg-gradient-primary rounded-xl p-6 text-white",children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsxs("h1",{className:"text-2xl font-bold",children:["欢迎回来，",e==null?void 0:e.username,"！"]}),l.jsx("p",{className:"mt-2 opacity-90",children:"今天是个开始互助点赞的好日子"})]}),l.jsx("div",{className:"hidden sm:block",children:l.jsx(ei,{className:"w-12 h-12 opacity-80"})})]})}),l.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map(a=>{const o=a.icon;return l.jsx("div",{className:"card card-hover",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:`p-3 rounded-lg ${a.bgColor}`,children:l.jsx(o,{className:`w-6 h-6 ${a.color}`})}),l.jsxs("div",{className:"ml-4 flex-1",children:[l.jsx("p",{className:"text-sm font-medium text-gray-600",children:a.name}),l.jsxs("div",{className:"flex items-center",children:[l.jsx("p",{className:"text-2xl font-semibold text-gray-900",children:a.value.toLocaleString()}),l.jsx("span",{className:"ml-2 text-sm text-green-600",children:a.change})]})]})]})},a.name)})}),l.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[l.jsx("div",{className:"lg:col-span-2",children:l.jsxs("div",{className:"card",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"快速操作"}),l.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:n.map(a=>{const o=a.icon;return l.jsx(Me,{to:a.href,className:`${a.color} text-white rounded-lg p-6 transition-colors group`,children:l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx(o,{className:"w-8 h-8 mb-3"}),l.jsx("h3",{className:"font-semibold",children:a.name}),l.jsx("p",{className:"text-sm opacity-90 mt-1",children:a.description})]}),l.jsx(ti,{className:"w-5 h-5 opacity-70 group-hover:opacity-100 transition-opacity"})]})},a.name)})})]})}),l.jsxs("div",{className:"card",children:[l.jsxs("div",{className:"flex items-center justify-between mb-6",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"最近活动"}),l.jsx(Me,{to:"/points",className:"text-sm text-primary-600 hover:text-primary-700",children:"查看全部"})]}),l.jsx("div",{className:"space-y-4",children:i.map(a=>l.jsxs("div",{className:"flex items-start space-x-3",children:[l.jsx("div",{className:"w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0"}),l.jsxs("div",{className:"flex-1 min-w-0",children:[l.jsx("p",{className:"text-sm font-medium text-gray-900",children:a.title}),l.jsx("p",{className:"text-sm text-gray-500",children:a.description}),l.jsx("p",{className:"text-xs text-gray-400 mt-1",children:a.time})]}),l.jsx("span",{className:`text-sm font-medium ${a.points.startsWith("+")?"text-green-600":"text-red-600"}`,children:a.points})]},a.id))})]})]}),l.jsxs("div",{className:"card",children:[l.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-6",children:"平台概览"}),l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("span",{className:"text-2xl",children:"📱"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"抖音"}),l.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"活跃任务 156 个"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("span",{className:"text-2xl",children:"⚡"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"快手"}),l.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"活跃任务 89 个"})]}),l.jsxs("div",{className:"text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("span",{className:"text-2xl",children:"📖"})}),l.jsx("h3",{className:"font-semibold text-gray-900",children:"小红书"}),l.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"活跃任务 67 个"})]})]})]})]})},bu=()=>{const[e,t]=T.useState({}),[r,s]=T.useState(""),[n,i]=T.useState(!1),a=[{id:1,title:"抖音美食视频点赞",description:"为我的美食制作视频点赞，内容健康正面",video_url:"https://douyin.com/video/123",platform:"douyin",task_type:"like",reward_points:5,total_quota:100,completed_count:23,expires_at:"2024-01-15T23:59:59Z",publisher_username:"foodlover123"},{id:2,title:"快手舞蹈视频分享",description:"分享我的舞蹈视频到朋友圈或其他平台",video_url:"https://kuaishou.com/video/456",platform:"kuaishou",task_type:"share",reward_points:8,total_quota:50,completed_count:12,expires_at:"2024-01-20T23:59:59Z",publisher_username:"dancer_girl"},{id:3,title:"小红书旅游笔记关注",description:"关注我的小红书账号，分享旅游攻略",video_url:"https://xiaohongshu.com/user/789",platform:"xiaohongshu",task_type:"follow",reward_points:10,total_quota:30,completed_count:8,expires_at:"2024-01-25T23:59:59Z",publisher_username:"travel_expert"}],{data:o,isLoading:c}=Vs(["tasks",e],()=>Promise.resolve({data:a}),{select:h=>h.data}),u=[{value:"douyin",label:"抖音",icon:"📱",color:"bg-red-100 text-red-800"},{value:"kuaishou",label:"快手",icon:"⚡",color:"bg-yellow-100 text-yellow-800"},{value:"xiaohongshu",label:"小红书",icon:"📖",color:"bg-pink-100 text-pink-800"}],f=[{value:"like",label:"点赞",icon:Ir},{value:"share",label:"分享",icon:si},{value:"follow",label:"关注",icon:ni},{value:"comment",label:"评论",icon:ii}],m=h=>{const w=f.find(O=>O.value===h);return w?w.icon:Ir},v=h=>u.find(w=>w.value===h),b=h=>{const w=new Date,j=new Date(h).getTime()-w.getTime(),N=Math.floor(j/(1e3*60*60*24)),k=Math.floor(j%(1e3*60*60*24)/(1e3*60*60));return N>0?`${N}天${k}小时`:k>0?`${k}小时`:"即将过期"},g=h=>{console.log("执行任务:",h)};return c?l.jsx("div",{className:"flex items-center justify-center h-64",children:l.jsx(We,{size:"lg",text:"加载任务中..."})}):l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{className:"flex items-center justify-between",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"任务大厅"}),l.jsx("p",{className:"text-gray-600 mt-1",children:"选择感兴趣的任务，赚取积分奖励"})]}),l.jsxs("div",{className:"text-sm text-gray-500",children:["共 ",(o==null?void 0:o.length)||0," 个任务"]})]}),l.jsxs("div",{className:"card",children:[l.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[l.jsxs("div",{className:"flex-1 relative",children:[l.jsx(qr,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),l.jsx("input",{type:"text",placeholder:"搜索任务标题...",value:r,onChange:h=>s(h.target.value),className:"input pl-10 w-full"})]}),l.jsxs("button",{onClick:()=>i(!n),className:"btn btn-outline flex items-center",children:[l.jsx(ri,{className:"w-4 h-4 mr-2"}),"筛选"]})]}),n&&l.jsx("div",{className:"mt-4 pt-4 border-t border-gray-200",children:l.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"平台"}),l.jsxs("select",{value:e.platform||"",onChange:h=>t({...e,platform:h.target.value||void 0}),className:"input w-full",children:[l.jsx("option",{value:"",children:"全部平台"}),u.map(h=>l.jsx("option",{value:h.value,children:h.label},h.value))]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"任务类型"}),l.jsxs("select",{value:e.task_type||"",onChange:h=>t({...e,task_type:h.target.value||void 0}),className:"input w-full",children:[l.jsx("option",{value:"",children:"全部类型"}),f.map(h=>l.jsx("option",{value:h.value,children:h.label},h.value))]})]}),l.jsxs("div",{children:[l.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"积分范围"}),l.jsxs("div",{className:"flex space-x-2",children:[l.jsx("input",{type:"number",placeholder:"最小",value:e.min_points||"",onChange:h=>t({...e,min_points:Number(h.target.value)||void 0}),className:"input flex-1"}),l.jsx("input",{type:"number",placeholder:"最大",value:e.max_points||"",onChange:h=>t({...e,max_points:Number(h.target.value)||void 0}),className:"input flex-1"})]})]})]})})]}),l.jsx("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:o==null?void 0:o.map(h=>{var N;const w=m(h.task_type),O=v(h.platform),j=h.completed_count/h.total_quota*100;return l.jsxs("div",{className:"card card-hover",children:[l.jsxs("div",{className:"flex items-start justify-between mb-4",children:[l.jsxs("div",{className:"flex-1",children:[l.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[l.jsxs("span",{className:`badge ${O==null?void 0:O.color}`,children:[O==null?void 0:O.icon," ",O==null?void 0:O.label]}),l.jsxs("span",{className:"badge badge-secondary",children:[l.jsx(w,{className:"w-3 h-3 mr-1"}),(N=f.find(k=>k.value===h.task_type))==null?void 0:N.label]})]}),l.jsx("h3",{className:"font-semibold text-gray-900 mb-1",children:h.title}),l.jsx("p",{className:"text-sm text-gray-600 text-ellipsis-2",children:h.description})]}),l.jsx("div",{className:"text-right",children:l.jsxs("div",{className:"flex items-center text-yellow-600 font-semibold",children:[l.jsx(jt,{className:"w-4 h-4 mr-1"}),h.reward_points]})})]}),l.jsxs("div",{className:"mb-4",children:[l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 mb-1",children:[l.jsx("span",{children:"完成进度"}),l.jsxs("span",{children:[h.completed_count,"/",h.total_quota]})]}),l.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:l.jsx("div",{className:"bg-primary-600 h-2 rounded-full transition-all",style:{width:`${j}%`}})})]}),l.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-500 mb-4",children:[l.jsxs("div",{className:"flex items-center",children:[l.jsx(ai,{className:"w-4 h-4 mr-1"}),b(h.expires_at)]}),l.jsxs("div",{className:"flex items-center",children:[l.jsx(_s,{className:"w-4 h-4 mr-1"}),h.publisher_username]})]}),l.jsxs("div",{className:"flex space-x-3",children:[l.jsx("button",{onClick:()=>g(h.id),disabled:j>=100,className:"btn btn-primary flex-1",children:j>=100?"已满额":"立即执行"}),l.jsx("a",{href:h.video_url,target:"_blank",rel:"noopener noreferrer",className:"btn btn-outline flex items-center",children:l.jsx(oi,{className:"w-4 h-4"})})]})]},h.id)})}),(o==null?void 0:o.length)===0&&l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx(qr,{className:"w-12 h-12 text-gray-400"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"暂无匹配的任务"}),l.jsx("p",{className:"text-gray-500",children:"尝试调整筛选条件或稍后再来看看"})]})]})},wu=()=>l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"发布任务"}),l.jsx("p",{className:"text-gray-600 mt-1",children:"创建新的互助任务，获得更多曝光"})]}),l.jsx("div",{className:"card",children:l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx("span",{className:"text-4xl",children:"🚧"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"功能开发中"}),l.jsx("p",{className:"text-gray-500",children:"发布任务功能正在开发中，敬请期待"})]})})]}),ju=()=>l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"我的任务"}),l.jsx("p",{className:"text-gray-600 mt-1",children:"管理您发布和执行的任务"})]}),l.jsx("div",{className:"card",children:l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx("span",{className:"text-4xl",children:"🚧"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"功能开发中"}),l.jsx("p",{className:"text-gray-500",children:"我的任务功能正在开发中，敬请期待"})]})})]}),Su=()=>l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"个人设置"}),l.jsx("p",{className:"text-gray-600 mt-1",children:"管理您的账号信息和偏好设置"})]}),l.jsx("div",{className:"card",children:l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx("span",{className:"text-4xl",children:"🚧"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"功能开发中"}),l.jsx("p",{className:"text-gray-500",children:"个人设置功能正在开发中，敬请期待"})]})})]}),Nu=()=>l.jsxs("div",{className:"space-y-6",children:[l.jsxs("div",{children:[l.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"积分记录"}),l.jsx("p",{className:"text-gray-600 mt-1",children:"查看您的积分收支明细"})]}),l.jsx("div",{className:"card",children:l.jsxs("div",{className:"text-center py-12",children:[l.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:l.jsx("span",{className:"text-4xl",children:"🚧"})}),l.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"功能开发中"}),l.jsx("p",{className:"text-gray-500",children:"积分记录功能正在开发中，敬请期待"})]})})]}),Eu=()=>{const[e,t]=T.useState(!1),{user:r,logout:s}=Re(),n=Kn(),i=[{name:"首页",href:"/dashboard",icon:ui},{name:"任务大厅",href:"/tasks",icon:ci},{name:"发布任务",href:"/tasks/create",icon:Rs},{name:"我的任务",href:"/my-tasks",icon:Xe},{name:"积分记录",href:"/points",icon:jt}],a=c=>n.pathname===c,o=()=>{s()};return l.jsxs("div",{className:"min-h-screen bg-gray-50",children:[e&&l.jsx("div",{className:"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden",onClick:()=>t(!1)}),l.jsx("div",{className:st("fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",e?"translate-x-0":"-translate-x-full"),children:l.jsxs("div",{className:"flex flex-col h-full",children:[l.jsxs("div",{className:"flex items-center justify-between h-16 px-6 border-b border-gray-200",children:[l.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"互助点赞"}),l.jsx("button",{onClick:()=>t(!1),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:l.jsx(li,{className:"w-5 h-5"})})]}),l.jsx("div",{className:"p-6 border-b border-gray-200",children:l.jsxs("div",{className:"flex items-center",children:[l.jsx("div",{className:"w-10 h-10 bg-primary-100 rounded-full flex items-center justify-center",children:l.jsx(Xe,{className:"w-5 h-5 text-primary-600"})}),l.jsxs("div",{className:"ml-3",children:[l.jsx("p",{className:"text-sm font-medium text-gray-900",children:r==null?void 0:r.username}),l.jsxs("p",{className:"text-xs text-gray-500",children:["积分: ",(r==null?void 0:r.points_balance)||0]})]})]})}),l.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2",children:i.map(c=>{const u=c.icon;return l.jsxs(Me,{to:c.href,className:st("flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",a(c.href)?"bg-primary-100 text-primary-700":"text-gray-600 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>t(!1),children:[l.jsx(u,{className:"w-5 h-5 mr-3"}),c.name]},c.name)})}),l.jsxs("div",{className:"p-4 border-t border-gray-200",children:[l.jsxs(Me,{to:"/profile",className:"flex items-center px-3 py-2 text-sm font-medium text-gray-600 rounded-lg hover:bg-gray-100 hover:text-gray-900 transition-colors mb-2",onClick:()=>t(!1),children:[l.jsx(Xe,{className:"w-5 h-5 mr-3"}),"个人设置"]}),l.jsxs("button",{onClick:o,className:"flex items-center w-full px-3 py-2 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 transition-colors",children:[l.jsx(di,{className:"w-5 h-5 mr-3"}),"退出登录"]})]})]})}),l.jsxs("div",{className:"lg:pl-64",children:[l.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:l.jsxs("div",{className:"flex items-center justify-between h-16 px-4 sm:px-6",children:[l.jsx("button",{onClick:()=>t(!0),className:"lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-600",children:l.jsx(fi,{className:"w-5 h-5"})}),l.jsxs("div",{className:"flex items-center space-x-4",children:[l.jsx("button",{className:"p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100",children:l.jsx(hi,{className:"w-5 h-5"})}),l.jsx("div",{className:"w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center",children:l.jsx(Xe,{className:"w-4 h-4 text-primary-600"})})]})]})}),l.jsx("main",{className:"flex-1",children:l.jsx("div",{className:"p-4 sm:p-6 lg:p-8",children:l.jsx(Wn,{})})})]})]})},Ou=({children:e})=>{const{isAuthenticated:t,isLoading:r}=Re();return r?l.jsx("div",{className:"min-h-screen flex items-center justify-center",children:l.jsx(We,{size:"lg",text:"加载中..."})}):t?l.jsx(l.Fragment,{children:e}):l.jsx(br,{to:"/login",replace:!0})},Ns=({children:e})=>{const{isAuthenticated:t,isLoading:r}=Re();return r?l.jsx("div",{className:"min-h-screen flex items-center justify-center",children:l.jsx(We,{size:"lg",text:"加载中..."})}):t?l.jsx(br,{to:"/dashboard",replace:!0}):l.jsx(l.Fragment,{children:e})},Ru=()=>{const{isAuthenticated:e,tokens:t,setLoading:r,logout:s}=Re();return T.useEffect(()=>{(async()=>{if(t!=null&&t.accessToken)try{r(!0),await at.getCurrentUser()}catch(i){console.error("获取用户信息失败:",i),s()}finally{r(!1)}else r(!1)})()},[t,r,s]),T.useEffect(()=>{const n=async()=>{if(document.visibilityState==="visible"&&e)try{await at.getCurrentUser()}catch(i){console.error("刷新用户信息失败:",i)}};return document.addEventListener("visibilitychange",n),()=>{document.removeEventListener("visibilitychange",n)}},[e]),l.jsx("div",{className:"min-h-screen bg-gray-50",children:l.jsxs(Jn,{children:[l.jsx(me,{path:"/login",element:l.jsx(Ns,{children:l.jsx(vu,{})})}),l.jsx(me,{path:"/register",element:l.jsx(Ns,{children:l.jsx(gu,{})})}),l.jsxs(me,{path:"/",element:l.jsx(Ou,{children:l.jsx(Eu,{})}),children:[l.jsx(me,{index:!0,element:l.jsx(br,{to:"/dashboard",replace:!0})}),l.jsx(me,{path:"dashboard",element:l.jsx(xu,{})}),l.jsx(me,{path:"tasks",element:l.jsx(bu,{})}),l.jsx(me,{path:"tasks/create",element:l.jsx(wu,{})}),l.jsx(me,{path:"my-tasks",element:l.jsx(ju,{})}),l.jsx(me,{path:"points",element:l.jsx(Nu,{})}),l.jsx(me,{path:"profile",element:l.jsx(Su,{})})]}),l.jsx(me,{path:"*",element:l.jsx("div",{className:"min-h-screen flex items-center justify-center",children:l.jsxs("div",{className:"text-center",children:[l.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"404"}),l.jsx("p",{className:"text-gray-600 mb-8",children:"页面不存在"}),l.jsx("button",{onClick:()=>window.history.back(),className:"btn btn-primary",children:"返回上一页"})]})})})]})})},_u=new Mi({defaultOptions:{queries:{retry:1,refetchOnWindowFocus:!1,staleTime:5*60*1e3},mutations:{retry:1}}});ar.createRoot(document.getElementById("root")).render(l.jsx(M.StrictMode,{children:l.jsx(Bi,{client:_u,children:l.jsxs(Gn,{children:[l.jsx(Ru,{}),l.jsx(Ma,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#22c55e",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#ef4444",secondary:"#fff"}}}})]})})}));
//# sourceMappingURL=index-BqWhr6oC.js.map
