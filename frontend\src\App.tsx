import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from '@/store/authStore';
import { AuthService } from '@/services/authService';

// 页面组件
import LoginPage from '@/pages/LoginPage';
import RegisterPage from '@/pages/RegisterPage';
import DashboardPage from '@/pages/DashboardPage';
import TaskHallPage from '@/pages/TaskHallPage';
import CreateTaskPage from '@/pages/CreateTaskPage';
import MyTasksPage from '@/pages/MyTasksPage';
import ProfilePage from '@/pages/ProfilePage';
import PointsPage from '@/pages/PointsPage';

// 布局组件
import Layout from '@/components/Layout';
import LoadingSpinner from '@/components/LoadingSpinner';
import DebugLogin from '@/components/DebugLogin';

// 路由保护组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading, user, tokens } = useAuthStore();

  console.log('🔧 ProtectedRoute 检查:', {
    isAuthenticated,
    isLoading,
    user: user?.username,
    hasTokens: !!tokens,
    currentPath: window.location.pathname
  });

  if (isLoading) {
    console.log('🔧 正在加载中...');
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="加载中..." />
      </div>
    );
  }

  if (!isAuthenticated) {
    console.log('🔧 用户未认证，重定向到登录页');
    console.log('🔧 详细状态:', { isAuthenticated, user, tokens });
    return <Navigate to="/login" replace />;
  }

  console.log('🔧 用户已认证，显示受保护内容');
  return <>{children}</>;
};

// 公开路由组件（已登录用户重定向到首页）
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" text="加载中..." />
      </div>
    );
  }

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }

  return <>{children}</>;
};

const App: React.FC = () => {
  const { isAuthenticated, tokens, setLoading, logout } = useAuthStore();

  // 应用渲染日志
  React.useEffect(() => {
    console.log('🔧 App 组件已渲染');
    console.log('🔧 当前路径:', window.location.pathname);
  }, []);

  // 应用初始化
  useEffect(() => {
    const initializeApp = async () => {
      // 如果有token，检查是否为模拟token
      if (tokens?.accessToken) {
        // 如果是模拟token（调试登录），跳过API验证
        if (tokens.accessToken.startsWith('mock_access_token_')) {
          setLoading(false);
          return;
        }

        // 真实token，尝试获取用户信息
        try {
          setLoading(true);
          await AuthService.getCurrentUser();
        } catch (error) {
          console.error('获取用户信息失败:', error);
          logout();
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    initializeApp();
  }, [tokens, setLoading, logout]);

  // 监听页面可见性变化，自动刷新token
  useEffect(() => {
    const handleVisibilityChange = async () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        // 如果是模拟token，跳过API刷新
        if (tokens?.accessToken?.startsWith('mock_access_token_')) {
          return;
        }

        try {
          await AuthService.getCurrentUser();
        } catch (error) {
          console.error('刷新用户信息失败:', error);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [isAuthenticated, tokens]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 调试登录组件 */}
      <DebugLogin />

      <Routes>
        {/* 公开路由 */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <LoginPage />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <RegisterPage />
            </PublicRoute>
          }
        />

        {/* 受保护的路由 */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          <Route path="tasks" element={<TaskHallPage />} />
          <Route path="tasks/create" element={<CreateTaskPage />} />
          <Route path="my-tasks" element={<MyTasksPage />} />
          <Route path="points" element={<PointsPage />} />
          <Route path="profile" element={<ProfilePage />} />
        </Route>

        {/* 404页面 */}
        <Route
          path="*"
          element={
            <div className="min-h-screen flex items-center justify-center">
              <div className="text-center">
                <h1 className="text-4xl font-bold text-gray-900 mb-4">404</h1>
                <p className="text-gray-600 mb-8">页面不存在</p>
                <button
                  onClick={() => window.history.back()}
                  className="btn btn-primary"
                >
                  返回上一页
                </button>
              </div>
            </div>
          }
        />
      </Routes>
    </div>
  );
};

export default App;
