const bcrypt = require('bcryptjs');

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> } 
 */
exports.seed = async function(knex) {
  // 清空现有数据
  await knex('point_transactions').del();
  await knex('task_executions').del();
  await knex('tasks').del();
  await knex('users').del();

  // 创建测试用户
  const hashedPassword = await bcrypt.hash('Password123', 12);
  
  const users = [
    {
      id: 1,
      username: 'admin',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      points_balance: 1000,
      social_accounts: JSON.stringify({}),
      is_active: true,
      email_verified: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: 2,
      username: 'testuser1',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      points_balance: 150,
      social_accounts: JSON.stringify({
        douyin: {
          user_id: 'dy123456',
          username: 'testuser1_dy',
          verified: true,
        },
      }),
      is_active: true,
      email_verified: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: 3,
      username: 'testuser2',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      points_balance: 200,
      social_accounts: JSON.stringify({
        kuaishou: {
          user_id: 'ks789012',
          username: 'testuser2_ks',
          verified: true,
        },
      }),
      is_active: true,
      email_verified: true,
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      id: 4,
      username: 'testuser3',
      email: '<EMAIL>',
      password_hash: hashedPassword,
      points_balance: 80,
      social_accounts: JSON.stringify({
        xiaohongshu: {
          user_id: 'xhs345678',
          username: 'testuser3_xhs',
          verified: true,
        },
      }),
      is_active: true,
      email_verified: false,
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  await knex('users').insert(users);

  // 创建初始积分记录
  const pointTransactions = [
    {
      user_id: 1,
      amount: 1000,
      transaction_type: 'bonus',
      description: '管理员初始积分',
      balance_after: 1000,
      metadata: JSON.stringify({}),
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      user_id: 2,
      amount: 100,
      transaction_type: 'bonus',
      description: '新用户注册奖励',
      balance_after: 100,
      metadata: JSON.stringify({}),
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      user_id: 2,
      amount: 50,
      transaction_type: 'bonus',
      description: '绑定社交账号奖励',
      balance_after: 150,
      metadata: JSON.stringify({ platform: 'douyin' }),
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      user_id: 3,
      amount: 100,
      transaction_type: 'bonus',
      description: '新用户注册奖励',
      balance_after: 100,
      metadata: JSON.stringify({}),
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      user_id: 3,
      amount: 100,
      transaction_type: 'bonus',
      description: '绑定社交账号奖励',
      balance_after: 200,
      metadata: JSON.stringify({ platform: 'kuaishou' }),
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      user_id: 4,
      amount: 100,
      transaction_type: 'bonus',
      description: '新用户注册奖励',
      balance_after: 100,
      metadata: JSON.stringify({}),
      created_at: new Date(),
      updated_at: new Date(),
    },
    {
      user_id: 4,
      amount: -20,
      transaction_type: 'cost',
      description: '发布任务消费',
      balance_after: 80,
      metadata: JSON.stringify({ task_id: 1 }),
      created_at: new Date(),
      updated_at: new Date(),
    },
  ];

  await knex('point_transactions').insert(pointTransactions);
};
