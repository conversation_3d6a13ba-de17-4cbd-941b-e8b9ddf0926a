"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskModel = void 0;
const database_1 = require("@/config/database");
class TaskModel {
    static async findById(id) {
        const task = await (0, database_1.db)(this.tableName).where({ id }).first();
        return task || null;
    }
    static async create(taskData) {
        const [task] = await (0, database_1.db)(this.tableName)
            .insert({
            ...taskData,
            verification_rules: JSON.stringify(taskData.verification_rules || {}),
        })
            .returning('*');
        return task;
    }
    static async findMany(filters = {}, page = 1, limit = 20) {
        let query = (0, database_1.db)(this.tableName)
            .select('tasks.*', 'users.username as publisher_username')
            .leftJoin('users', 'tasks.publisher_id', 'users.id')
            .where('tasks.status', 'active')
            .where('tasks.expires_at', '>', new Date())
            .where('tasks.completed_count', '<', database_1.db.raw('tasks.total_quota'));
        if (filters.platform) {
            query = query.where('tasks.platform', filters.platform);
        }
        if (filters.task_type) {
            query = query.where('tasks.task_type', filters.task_type);
        }
        if (filters.min_points) {
            query = query.where('tasks.reward_points', '>=', filters.min_points);
        }
        if (filters.max_points) {
            query = query.where('tasks.reward_points', '<=', filters.max_points);
        }
        if (filters.search) {
            query = query.where('tasks.title', 'ilike', `%${filters.search}%`);
        }
        const totalQuery = query.clone();
        const countResult = await totalQuery.count('* as count');
        const total = Number(countResult[0]?.count) || 0;
        const offset = (page - 1) * limit;
        const tasks = await query
            .orderBy('tasks.created_at', 'desc')
            .limit(limit)
            .offset(offset);
        const totalPages = Math.ceil(total / limit);
        return {
            tasks,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        };
    }
    static async findByPublisher(publisherId, page = 1, limit = 20) {
        const offset = (page - 1) * limit;
        const countResult = await (0, database_1.db)(this.tableName)
            .where({ publisher_id: publisherId })
            .count('* as count');
        const tasks = await (0, database_1.db)(this.tableName)
            .where({ publisher_id: publisherId })
            .orderBy('created_at', 'desc')
            .limit(limit)
            .offset(offset);
        const total = Number(countResult[0]?.count) || 0;
        const totalPages = Math.ceil(total / limit);
        return {
            tasks,
            total,
            totalPages,
        };
    }
    static async updateStatus(id, status) {
        const [task] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .update({
            status,
            updated_at: new Date(),
        })
            .returning('*');
        return task || null;
    }
    static async incrementCompletedCount(id) {
        const [task] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .increment('completed_count', 1)
            .update({ updated_at: new Date() })
            .returning('*');
        return task || null;
    }
    static async decrementCompletedCount(id) {
        const [task] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .where('completed_count', '>', 0)
            .decrement('completed_count', 1)
            .update({ updated_at: new Date() })
            .returning('*');
        return task || null;
    }
    static async canExecute(taskId, userId) {
        const task = await this.findById(taskId);
        if (!task) {
            return { canExecute: false, reason: '任务不存在' };
        }
        if (task.status !== 'active') {
            return { canExecute: false, reason: '任务已暂停或完成' };
        }
        if (new Date() > new Date(task.expires_at)) {
            return { canExecute: false, reason: '任务已过期' };
        }
        if (task.completed_count >= task.total_quota) {
            return { canExecute: false, reason: '任务名额已满' };
        }
        if (task.publisher_id === userId) {
            return { canExecute: false, reason: '不能执行自己发布的任务' };
        }
        const existingExecution = await (0, database_1.db)('task_executions')
            .where({ task_id: taskId, executor_id: userId })
            .first();
        if (existingExecution) {
            return { canExecute: false, reason: '已经执行过该任务' };
        }
        return { canExecute: true };
    }
    static async getTaskDetails(id) {
        const task = await (0, database_1.db)(this.tableName)
            .select('tasks.*', 'users.username as publisher_username')
            .leftJoin('users', 'tasks.publisher_id', 'users.id')
            .where('tasks.id', id)
            .first();
        return task || null;
    }
    static async update(id, updateData) {
        const [task] = await (0, database_1.db)(this.tableName)
            .where({ id })
            .update({
            ...updateData,
            updated_at: new Date(),
        })
            .returning('*');
        return task || null;
    }
    static async delete(id) {
        const deletedCount = await (0, database_1.db)(this.tableName).where({ id }).del();
        return deletedCount > 0;
    }
    static async getPlatformStats() {
        const stats = await (0, database_1.db)(this.tableName)
            .select('platform')
            .count('* as count')
            .where('status', 'active')
            .groupBy('platform');
        const result = {
            douyin: 0,
            kuaishou: 0,
            xiaohongshu: 0,
        };
        stats.forEach((stat) => {
            result[stat.platform] = Number(stat.count);
        });
        return result;
    }
    static async updateExpiredTasks() {
        const updatedCount = await (0, database_1.db)(this.tableName)
            .where('status', 'active')
            .where('expires_at', '<', new Date())
            .update({
            status: 'expired',
            updated_at: new Date(),
        });
        return updatedCount;
    }
}
exports.TaskModel = TaskModel;
TaskModel.tableName = 'tasks';
//# sourceMappingURL=Task.js.map