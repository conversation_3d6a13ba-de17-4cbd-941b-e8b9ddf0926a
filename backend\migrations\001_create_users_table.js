/**
 * 创建用户表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('users', function(table) {
    table.increments('id').primary();
    table.string('username', 50).notNullable().unique();
    table.string('email', 100).notNullable().unique();
    table.string('password_hash', 255).notNullable();
    table.integer('points_balance').defaultTo(100).notNullable();
    table.json('social_accounts').defaultTo('{}');
    table.boolean('is_active').defaultTo(true);
    table.string('email_verification_token', 255);
    table.boolean('email_verified').defaultTo(false);
    table.string('password_reset_token', 255);
    table.timestamp('password_reset_expires');
    table.string('last_login_ip', 45);
    table.timestamp('last_login_at');
    table.timestamps(true, true);
    
    // 索引
    table.index(['email']);
    table.index(['username']);
    table.index(['is_active']);
    table.index(['email_verified']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('users');
};
