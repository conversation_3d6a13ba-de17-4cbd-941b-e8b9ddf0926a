import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import {
  TrendingUp,
  Users,
  Target,
  Coins,
  Plus,
  ArrowRight,
  Calendar,
  Award,
} from 'lucide-react';
import { useAuthStore } from '@/store/authStore';
import { AuthService } from '@/services/authService';
import LoadingSpinner from '@/components/LoadingSpinner';

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();

  // 获取用户统计数据
  const { data: userStats, isLoading, error } = useQuery(
    'userStats',
    AuthService.getCurrentUser,
    {
      select: (response) => response.data?.stats,
      retry: 1,
      staleTime: 5 * 60 * 1000, // 5分钟
      refetchOnWindowFocus: false,
      onError: (error) => {
        console.warn('获取用户统计失败:', error);
      },
    }
  );

  // 只在初次加载时显示loading，如果有错误则继续显示页面
  if (isLoading && !error) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" text="加载中..." />
      </div>
    );
  }

  const stats = [
    {
      name: '当前积分',
      value: user?.points_balance || 0,
      icon: Coins,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-100',
      change: '+12',
      changeType: 'increase',
    },
    {
      name: '发布任务',
      value: userStats?.totalTasksPublished || 0,
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
      change: '+2',
      changeType: 'increase',
    },
    {
      name: '完成任务',
      value: userStats?.totalTasksCompleted || 0,
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
      change: '+5',
      changeType: 'increase',
    },
    {
      name: '总收益',
      value: userStats?.totalPointsEarned || 0,
      icon: TrendingUp,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
      change: '+18',
      changeType: 'increase',
    },
  ];

  const quickActions = [
    {
      name: '发布任务',
      description: '发布新的点赞任务',
      href: '/tasks/create',
      icon: Plus,
      color: 'bg-blue-600 hover:bg-blue-700',
    },
    {
      name: '任务大厅',
      description: '浏览可执行的任务',
      href: '/tasks',
      icon: Users,
      color: 'bg-green-600 hover:bg-green-700',
    },
    {
      name: '积分记录',
      description: '查看积分流水',
      href: '/points',
      icon: Coins,
      color: 'bg-yellow-600 hover:bg-yellow-700',
    },
  ];

  const recentActivities = [
    {
      id: 1,
      type: 'task_completed',
      title: '完成点赞任务',
      description: '抖音视频点赞任务',
      points: '+5',
      time: '2分钟前',
    },
    {
      id: 2,
      type: 'task_published',
      title: '发布新任务',
      description: '快手视频点赞任务',
      points: '-20',
      time: '1小时前',
    },
    {
      id: 3,
      type: 'reward',
      title: '获得奖励',
      description: '连续签到7天奖励',
      points: '+10',
      time: '昨天',
    },
  ];

  return (
    <div className="space-y-8">
      {/* 欢迎信息 */}
      <div className="gradient-primary rounded-xl p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold">
              欢迎回来，{user?.username}！
            </h1>
            <p className="mt-2 opacity-90">
              今天是个开始互助点赞的好日子
            </p>
          </div>
          <div className="hidden sm:block">
            <Calendar className="w-12 h-12 opacity-80" />
          </div>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <div key={stat.name} className="card card-hover">
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                  <Icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium text-gray-600">
                    {stat.name}
                  </p>
                  <div className="flex items-center">
                    <p className="text-2xl font-semibold text-gray-900">
                      {stat.value.toLocaleString()}
                    </p>
                    <span className="ml-2 text-sm text-green-600">
                      {stat.change}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* 快速操作 */}
        <div className="xl:col-span-2">
          <div className="card">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">
              快速操作
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {quickActions.map((action) => {
                const Icon = action.icon;
                return (
                  <Link
                    key={action.name}
                    to={action.href}
                    className={`${action.color} text-white rounded-lg p-6 transition-colors group block min-h-[120px]`}
                  >
                    <div className="flex flex-col h-full justify-between">
                      <div>
                        <Icon className="w-8 h-8 mb-3" />
                        <h3 className="font-semibold text-lg mb-2">{action.name}</h3>
                        <p className="text-sm opacity-90">
                          {action.description}
                        </p>
                      </div>
                      <div className="flex justify-end mt-3">
                        <ArrowRight className="w-5 h-5 opacity-70 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          </div>
        </div>

        {/* 最近活动 */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-gray-900">
              最近活动
            </h2>
            <Link
              to="/points"
              className="text-sm text-primary-600 hover:text-primary-700"
            >
              查看全部
            </Link>
          </div>
          <div className="space-y-4">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-primary-600 rounded-full mt-2 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-sm text-gray-500">
                    {activity.description}
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    {activity.time}
                  </p>
                </div>
                <span
                  className={`text-sm font-medium ${
                    activity.points.startsWith('+')
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {activity.points}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 平台统计 */}
      <div className="card">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">
          平台概览
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">📱</span>
            </div>
            <h3 className="font-semibold text-gray-900">抖音</h3>
            <p className="text-sm text-gray-500 mt-1">
              活跃任务 156 个
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">⚡</span>
            </div>
            <h3 className="font-semibold text-gray-900">快手</h3>
            <p className="text-sm text-gray-500 mt-1">
              活跃任务 89 个
            </p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-3">
              <span className="text-2xl">📖</span>
            </div>
            <h3 className="font-semibold text-gray-900">小红书</h3>
            <p className="text-sm text-gray-500 mt-1">
              活跃任务 67 个
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
