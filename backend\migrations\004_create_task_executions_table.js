/**
 * 创建任务执行记录表
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.createTable('task_executions', function(table) {
    table.increments('id').primary();
    table.integer('task_id').unsigned().notNullable();
    table.integer('executor_id').unsigned().notNullable();
    table.enum('status', ['pending', 'completed', 'failed', 'verified', 'rejected']).defaultTo('pending');
    table.timestamp('submitted_at').defaultTo(knex.fn.now());
    table.timestamp('verified_at').nullable();
    table.json('verification_result').defaultTo('{}');
    table.string('ip_address', 45).notNullable();
    table.string('user_agent', 500);
    table.json('execution_proof').defaultTo('{}'); // 执行证明数据
    table.text('rejection_reason'); // 拒绝原因
    table.timestamps(true, true);
    
    // 外键约束
    table.foreign('task_id').references('id').inTable('tasks').onDelete('CASCADE');
    table.foreign('executor_id').references('id').inTable('users').onDelete('CASCADE');
    
    // 唯一约束：同一用户不能重复执行同一任务
    table.unique(['task_id', 'executor_id']);
    
    // 索引
    table.index(['task_id']);
    table.index(['executor_id']);
    table.index(['status']);
    table.index(['submitted_at']);
    table.index(['ip_address']);
    table.index(['executor_id', 'submitted_at']);
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.dropTable('task_executions');
};
