import React, { useState, useEffect } from 'react';

interface DemoInfo {
  mode: string;
  features: string[];
  setup: {
    title: string;
    steps: string[];
    docker: string;
  };
}

interface DemoUser {
  id: number;
  username: string;
  email: string;
  points_balance: number;
  email_verified: boolean;
  social_accounts: any;
  created_at: string;
}

interface DemoTask {
  id: number;
  title: string;
  description: string;
  platform: string;
  task_type: string;
  reward_points: number;
  total_quota: number;
  completed_count: number;
  status: string;
  publisher_username: string;
}

const DemoPage: React.FC = () => {
  const [demoInfo, setDemoInfo] = useState<DemoInfo | null>(null);
  const [demoUser, setDemoUser] = useState<DemoUser | null>(null);
  const [demoTasks, setDemoTasks] = useState<DemoTask[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDemoData = async () => {
      try {
        // 获取演示信息
        const infoResponse = await fetch('http://localhost:3000/api/demo/info');
        const infoData = await infoResponse.json();
        setDemoInfo(infoData.data);

        // 获取演示用户
        const userResponse = await fetch('http://localhost:3000/api/demo/user');
        const userData = await userResponse.json();
        setDemoUser(userData.data.user);

        // 获取演示任务
        const tasksResponse = await fetch('http://localhost:3000/api/demo/tasks');
        const tasksData = await tasksResponse.json();
        setDemoTasks(tasksData.data.tasks);

        setLoading(false);
      } catch (error) {
        console.error('获取演示数据失败:', error);
        setLoading(false);
      }
    };

    fetchDemoData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-xl">加载中...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* 头部 */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">互赞平台</h1>
              <span className="ml-3 px-3 py-1 bg-yellow-100 text-yellow-800 text-sm font-medium rounded-full">
                演示模式
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-600">积分: {demoUser?.points_balance || 0}</span>
              <span className="text-gray-600">用户: {demoUser?.username || 'demo_user'}</span>
              <a href="/login" className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
                登录体验
              </a>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 演示信息卡片 */}
        {demoInfo && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">
              🎯 {demoInfo.setup.title}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-medium text-blue-800 mb-2">当前功能状态:</h3>
                <ul className="space-y-1">
                  {demoInfo.features.map((feature, index) => (
                    <li key={index} className="text-sm text-blue-700">{feature}</li>
                  ))}
                </ul>
              </div>
              <div>
                <h3 className="font-medium text-blue-800 mb-2">启用完整功能步骤:</h3>
                <ol className="space-y-1">
                  {demoInfo.setup.steps.map((step, index) => (
                    <li key={index} className="text-sm text-blue-700">{step}</li>
                  ))}
                </ol>
                <div className="mt-3 p-2 bg-gray-100 rounded text-xs font-mono text-gray-600">
                  {demoInfo.setup.docker}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 任务列表 */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">可用任务</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {demoTasks.map((task) => (
              <div key={task.id} className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900">{task.title}</h3>
                    <p className="mt-1 text-sm text-gray-600">{task.description}</p>
                    <div className="mt-2 flex items-center space-x-4 text-sm text-gray-500">
                      <span>平台: {task.platform}</span>
                      <span>类型: {task.task_type}</span>
                      <span>发布者: {task.publisher_username}</span>
                    </div>
                  </div>
                  <div className="ml-6 flex flex-col items-end">
                    <div className="text-lg font-semibold text-green-600">
                      +{task.reward_points} 积分
                    </div>
                    <div className="text-sm text-gray-500">
                      {task.completed_count}/{task.total_quota}
                    </div>
                    <button className="mt-2 px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
                            disabled>
                      演示模式
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 底部说明 */}
        <div className="mt-8 text-center text-gray-500">
          <p>这是互赞平台的演示版本。要体验完整功能，请配置数据库并重启服务器。</p>
          <p className="mt-2">
            <a href="http://localhost:3000/api-docs" target="_blank" rel="noopener noreferrer" 
               className="text-blue-600 hover:text-blue-800">
              查看 API 文档
            </a>
            {' | '}
            <a href="http://localhost:3000/health" target="_blank" rel="noopener noreferrer"
               className="text-blue-600 hover:text-blue-800">
              健康检查
            </a>
          </p>
        </div>
      </main>
    </div>
  );
};

export default DemoPage;
