@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-gray-200;
  }

  body {
    @apply bg-gray-50 text-gray-900;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  html {
    scroll-behavior: smooth;
  }
  
  /* 自定义滚动条 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
  
  /* Firefox 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
}

@layer components {
  /* 按钮组件样式 */
  .btn {
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 active:bg-primary-800;
  }
  
  .btn-secondary {
    @apply bg-secondary-100 text-secondary-900 hover:bg-secondary-200 active:bg-secondary-300;
  }
  
  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 active:bg-gray-100;
  }
  
  .btn-ghost {
    @apply text-gray-700 hover:bg-gray-100 active:bg-gray-200;
  }
  
  .btn-danger {
    @apply bg-error-600 text-white hover:bg-error-700 active:bg-error-800;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 active:bg-success-800;
  }
  
  /* 输入框组件样式 */
  .input {
    @apply flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }
  
  .input-error {
    @apply border-error-500 focus-visible:ring-error-500;
  }
  
  /* 卡片组件样式 */
  .card {
    @apply rounded-xl border border-gray-200 bg-white p-6 shadow-sm;
  }
  
  .card-hover {
    @apply transition-shadow hover:shadow-md;
  }
  
  /* 徽章组件样式 */
  .badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-error {
    @apply bg-error-100 text-error-800;
  }
  
  /* 加载动画 */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  /* 渐变背景 */
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  }
  
  /* 玻璃态效果 */
  .glass {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }
  
  /* 阴影效果 */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.15);
  }
}

@layer utilities {
  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }
  
  .text-ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .text-ellipsis-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* 居中布局 */
  .center {
    @apply flex items-center justify-center;
  }
  
  .center-x {
    @apply flex justify-center;
  }
  
  .center-y {
    @apply flex items-center;
  }
  
  /* 全屏布局 */
  .full-screen {
    @apply min-h-screen w-full;
  }
  
  /* 容器最大宽度 */
  .container-sm {
    @apply mx-auto max-w-2xl px-4;
  }
  
  .container-md {
    @apply mx-auto max-w-4xl px-4;
  }
  
  .container-lg {
    @apply mx-auto max-w-6xl px-4;
  }
  
  .container-xl {
    @apply mx-auto max-w-7xl px-4;
  }
  
  /* 响应式间距 */
  .space-y-responsive {
    @apply space-y-4 md:space-y-6;
  }
  
  .space-x-responsive {
    @apply space-x-4 md:space-x-6;
  }
  
  /* 安全区域 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
}
