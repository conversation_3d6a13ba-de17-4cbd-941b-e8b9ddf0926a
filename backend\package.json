{"name": "mutual-like-backend", "version": "1.0.0", "description": "互助点赞平台后端API服务", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:migrate": "knex migrate:latest", "db:rollback": "knex migrate:rollback", "db:seed": "knex seed:run", "db:reset": "knex migrate:rollback --all && knex migrate:latest && knex seed:run", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["nodejs", "express", "typescript", "postgresql", "jwt"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "nodemailer": "^6.9.7", "pg": "^8.11.3", "knex": "^3.0.1", "redis": "^4.6.10", "multer": "^1.4.5-lts.1", "sharp": "^0.32.6", "axios": "^1.6.2", "dotenv": "^16.3.1", "winston": "^3.11.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/pg": "^8.10.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}